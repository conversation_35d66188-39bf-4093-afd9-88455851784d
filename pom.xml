<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>spring-boot-starter-parent</artifactId>
    <groupId>org.springframework.boot</groupId>
    <version>2.7.18</version>
    <relativePath/>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.bestpay.media.platform</groupId>
  <artifactId>rc-gateway</artifactId>
  <packaging>pom</packaging>
  <name>rc-gateway</name>
  <version>0.5.4</version>
  <modules>
    <module>rc-common</module>
    <module>rc-gateway-background</module>
    <module>rc-encrypt</module>
    <module>rc-test</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.bestpay.media.platform</groupId>
        <artifactId>rc-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.bestpay.media.platform</groupId>
        <artifactId>rc-gateway-background</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-bom</artifactId>
        <version>${log4j2.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-reload4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>log4j2-ttl-thread-context-map</artifactId>
        <version>${log4j2-ttl-thread-context-map.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.phantomthief</groupId>
        <artifactId>simple-failover</artifactId>
        <version>${simple-failover.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>${disruptor.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.resteasy</groupId>
        <artifactId>resteasy-spring-boot-starter</artifactId>
        <version>${resteasy-spring-boot-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson-spring-boot-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis-spring</artifactId>
        <version>${mybatis-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>${oracle.version}</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.nls</groupId>
        <artifactId>orai18n</artifactId>
        <version>${oracle.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>${error_prone_annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.ws.rs</groupId>
        <artifactId>javax.ws.rs-api</artifactId>
        <version>${javax.ws.rs-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.resteasy</groupId>
        <artifactId>resteasy-client</artifactId>
        <version>${resteasy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.resteasy</groupId>
        <artifactId>resteasy-jackson2-provider</artifactId>
        <version>${resteasy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.resteasy</groupId>
        <artifactId>resteasy-jaxb-provider</artifactId>
        <version>${resteasy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>co.elastic.clients</groupId>
        <artifactId>elasticsearch-java</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>parent-join-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>aggs-matrix-stats-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>rank-eval-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>lang-mustache-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.javassist</groupId>
        <artifactId>javassist</artifactId>
        <version>${javassist.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>

      <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>${jakarta.json.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>
  <properties>
    <asm.version>9.0</asm.version>
    <checker-qual.version>3.8.0</checker-qual.version>
    <commons-beanutils.version>1.9.4</commons-beanutils.version>
    <commons-configuration2.version>2.5</commons-configuration2.version>
    <commons-io.version>2.16.0</commons-io.version>
    <disruptor.version>3.4.4</disruptor.version>
    <elasticsearch-java.version>8.15.0</elasticsearch-java.version>
    <error_prone_annotations.version>2.4.0</error_prone_annotations.version>
    <fastjson.version>2.0.48</fastjson.version>
    <guava.version>33.0.0-jre</guava.version>
    <java.version>1.8</java.version>
    <javassist.version>3.30.2-GA</javassist.version>
    <javax.ws.rs-api.version>2.1</javax.ws.rs-api.version>
    <log4j2-ttl-thread-context-map.version>1.4.0</log4j2-ttl-thread-context-map.version>
    <log4j2.version>2.23.1</log4j2.version>
    <lombok.version>1.18.32</lombok.version>
    <mybatis-spring.version>1.3.1</mybatis-spring.version>
    <mybatis.version>3.5.9</mybatis.version>
    <oracle.version>21.1.0.0</oracle.version>
    <redisson-spring-boot-starter.version>3.27.2</redisson-spring-boot-starter.version>
    <resteasy-spring-boot-starter.version>4.9.0.Final</resteasy-spring-boot-starter.version>
    <resteasy.version>4.7.0.Final</resteasy.version>
    <simple-failover.version>0.1.32</simple-failover.version>
    <slf4j.version>2.0.12</slf4j.version>
    <snakeyaml.version>1.33</snakeyaml.version>
    <spring-jdbc.version>5.3.9</spring-jdbc.version>
    <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
    <jakarta.json.version>2.1.3</jakarta.json.version>
  </properties>
</project>
