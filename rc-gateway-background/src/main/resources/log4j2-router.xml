<?xml version="1.0" encoding="UTF-8"?>
<RollingRandomAccessFile fileName="${logDirect}/${logFileName}.log"
  filePattern="${logDirect}/${logFileName}.%d{yyyy-MM-dd}.%i.log"
  immediateFlush="false" name="router">
  <DefaultRolloverStrategy max="50000"/>
  <Filters>
    <ThresholdFilter level="${loggerLogLevel}" onMatch="ACCEPT" onMismatch="DENY"/>
  </Filters>
  <PatternLayout>
    <MarkerPatternSelector defaultPattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%logger %line][%method]%message%n">
      <PatternMatch key="PARAMETER"
        pattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%mdc{CLASS_NAME} %mdc{METHOD_LINE}][%mdc{METHOD_NAME}]%message%n"/>
      <PatternMatch key="RESPONSE"
        pattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%mdc{CLASS_NAME} %mdc{METHOD_LINE}][%mdc{METHOD_NAME}][%mdc{RESPONSE_CODE}][%mdc{RESPONSE_STATUS}][%mdc{ELAPSED_TIME}]%message%n"/>
    </MarkerPatternSelector>
  </PatternLayout>
  <Policies>
    <SizeBasedTriggeringPolicy size="512MB"/>
    <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
  </Policies>
</RollingRandomAccessFile>