<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.media.platform.rc.gateway.background.dal.mapper.RiskImageLogMapper">
  <resultMap id="BaseResultMap" type="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO">
    <id column="SERIALNO" jdbcType="NVARCHAR" property="serialno"/>
    <result column="RPFSERIALNO" jdbcType="NVARCHAR" property="rpfserialno"/>
    <result column="CERTID" jdbcType="NVARCHAR" property="certid"/>
    <result column="CUSTOMER_NO" jdbcType="NVARCHAR" property="customerNo"/>
    <result column="CUSTOMERNAME" jdbcType="NVARCHAR" property="customername"/>
    <result column="MOBILE_PROV_NM" jdbcType="NVARCHAR" property="mobileProvNm"/>
    <result column="ID_NO_PROV_NM" jdbcType="NVARCHAR" property="idNoProvNm"/>
    <result column="LIVE_PROVINCE" jdbcType="NVARCHAR" property="liveProvince"/>
    <result column="IP_PROV_CN" jdbcType="NVARCHAR" property="ipProvCn"/>
    <result column="SUCCESS" jdbcType="VARCHAR" property="success"/>
    <result column="RESULT" jdbcType="NVARCHAR" property="result"/>
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime"/>
    <result column="RESPONSETIME" jdbcType="FLOAT" property="responsetime"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SERIALNO
    , RPFSERIALNO, CERTID, CUSTOMER_NO, CUSTOMERNAME, MOBILE_PROV_NM, ID_NO_PROV_NM,
    LIVE_PROVINCE, IP_PROV_CN, SUCCESS, RESULT, CREATETIME, RESPONSETIME
  </sql>
  <select id="selectByExample" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogQuery"
    resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from T_LOG_RISKIMAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List"/>
      </otherwise>
    </choose>
    from T_LOG_RISKIMAGE
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_LOG_RISKIMAGE
    where SERIALNO = #{serialno,jdbcType=NVARCHAR}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List"/>
      </otherwise>
    </choose>
    from T_LOG_RISKIMAGE
    where SERIALNO = #{serialno,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete
    from T_LOG_RISKIMAGE
    where SERIALNO = #{serialno,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogQuery">
    delete from T_LOG_RISKIMAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </delete>
  <insert id="insert" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO">
    insert into T_LOG_RISKIMAGE (SERIALNO, RPFSERIALNO, CERTID,
                                 CUSTOMER_NO, CUSTOMERNAME, MOBILE_PROV_NM,
                                 ID_NO_PROV_NM, LIVE_PROVINCE, IP_PROV_CN,
                                 SUCCESS, RESULT, CREATETIME,
                                 RESPONSETIME)
    values (#{serialno,jdbcType=NVARCHAR}, #{rpfserialno,jdbcType=NVARCHAR}, #{certid,jdbcType=NVARCHAR},
            #{customerNo,jdbcType=NVARCHAR}, #{customername,jdbcType=NVARCHAR}, #{mobileProvNm,jdbcType=NVARCHAR},
            #{idNoProvNm,jdbcType=NVARCHAR}, #{liveProvince,jdbcType=NVARCHAR}, #{ipProvCn,jdbcType=NVARCHAR},
            #{success,jdbcType=VARCHAR}, #{result,jdbcType=NVARCHAR}, #{createtime,jdbcType=TIMESTAMP},
            #{responsetime,jdbcType=FLOAT})
  </insert>
  <insert id="batchInsert" parameterType="map" useGeneratedKeys="false">
    insert into ${scheme}.${tableName} (SERIALNO, RPFSERIALNO, CERTID,
    CUSTOMER_NO, CUSTOMERNAME, MOBILE_PROV_NM,
    ID_NO_PROV_NM, LIVE_PROVINCE, IP_PROV_CN,
    SUCCESS, RESULT, CREATETIME,
    RESPONSETIME)
    <foreach item="item" index="index" collection="list" separator="union all">
      (
      SELECT
      #{item.serialno,jdbcType=NVARCHAR}, #{item.rpfserialno,jdbcType=NVARCHAR}, #{item.certid,jdbcType=NVARCHAR},
      #{item.customerNo,jdbcType=NVARCHAR}, #{item.customername,jdbcType=NVARCHAR}, #{item.mobileProvNm,jdbcType=NVARCHAR},
      #{item.idNoProvNm,jdbcType=NVARCHAR}, #{item.liveProvince,jdbcType=NVARCHAR}, #{item.ipProvCn,jdbcType=NVARCHAR},
      #{item.success,jdbcType=VARCHAR}, #{item.result,jdbcType=NVARCHAR}, #{item.createtime,jdbcType=TIMESTAMP},
      #{item.responsetime,jdbcType=FLOAT}
      FROM DUAL
      )
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="map">
    insert into T_LOG_RISKIMAGE
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach close=")" collection="selective" item="column" open="(" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="record.serialno != null">
            SERIALNO,
          </if>
          <if test="record.rpfserialno != null">
            RPFSERIALNO,
          </if>
          <if test="record.certid != null">
            CERTID,
          </if>
          <if test="record.customerNo != null">
            CUSTOMER_NO,
          </if>
          <if test="record.customername != null">
            CUSTOMERNAME,
          </if>
          <if test="record.mobileProvNm != null">
            MOBILE_PROV_NM,
          </if>
          <if test="record.idNoProvNm != null">
            ID_NO_PROV_NM,
          </if>
          <if test="record.liveProvince != null">
            LIVE_PROVINCE,
          </if>
          <if test="record.ipProvCn != null">
            IP_PROV_CN,
          </if>
          <if test="record.success != null">
            SUCCESS,
          </if>
          <if test="record.result != null">
            RESULT,
          </if>
          <if test="record.createtime != null">
            CREATETIME,
          </if>
          <if test="record.responsetime != null">
            RESPONSETIME,
          </if>
        </trim>
        <trim prefix="(" suffix=")" suffixOverrides=","/>
      </otherwise>
    </choose>
    values
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach close=")" collection="selective" item="column" open="(" separator=",">
          #{record.${column.javaProperty},jdbcType=${column.jdbcType}}
        </foreach>
      </when>
      <otherwise>
        <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="record.serialno != null">
            #{record.serialno,jdbcType=NVARCHAR},
          </if>
          <if test="record.rpfserialno != null">
            #{record.rpfserialno,jdbcType=NVARCHAR},
          </if>
          <if test="record.certid != null">
            #{record.certid,jdbcType=NVARCHAR},
          </if>
          <if test="record.customerNo != null">
            #{record.customerNo,jdbcType=NVARCHAR},
          </if>
          <if test="record.customername != null">
            #{record.customername,jdbcType=NVARCHAR},
          </if>
          <if test="record.mobileProvNm != null">
            #{record.mobileProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.idNoProvNm != null">
            #{record.idNoProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.liveProvince != null">
            #{record.liveProvince,jdbcType=NVARCHAR},
          </if>
          <if test="record.ipProvCn != null">
            #{record.ipProvCn,jdbcType=NVARCHAR},
          </if>
          <if test="record.success != null">
            #{record.success,jdbcType=VARCHAR},
          </if>
          <if test="record.result != null">
            #{record.result,jdbcType=NVARCHAR},
          </if>
          <if test="record.createtime != null">
            #{record.createtime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.responsetime != null">
            #{record.responsetime,jdbcType=FLOAT},
          </if>
        </trim>
      </otherwise>
    </choose>
  </insert>
  <select id="countByExample" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogQuery"
    resultType="java.lang.Long">
    select count(*) from T_LOG_RISKIMAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update T_LOG_RISKIMAGE
    SET
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName} = #{record.${column.javaProperty},jdbcType=${column.jdbcType}}
        </foreach>
      </when>
      <otherwise>
        <trim suffixOverrides=",">
          <if test="record.serialno != null">
            SERIALNO = #{record.serialno,jdbcType=NVARCHAR},
          </if>
          <if test="record.rpfserialno != null">
            RPFSERIALNO = #{record.rpfserialno,jdbcType=NVARCHAR},
          </if>
          <if test="record.certid != null">
            CERTID = #{record.certid,jdbcType=NVARCHAR},
          </if>
          <if test="record.customerNo != null">
            CUSTOMER_NO = #{record.customerNo,jdbcType=NVARCHAR},
          </if>
          <if test="record.customername != null">
            CUSTOMERNAME = #{record.customername,jdbcType=NVARCHAR},
          </if>
          <if test="record.mobileProvNm != null">
            MOBILE_PROV_NM = #{record.mobileProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.idNoProvNm != null">
            ID_NO_PROV_NM = #{record.idNoProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.liveProvince != null">
            LIVE_PROVINCE = #{record.liveProvince,jdbcType=NVARCHAR},
          </if>
          <if test="record.ipProvCn != null">
            IP_PROV_CN = #{record.ipProvCn,jdbcType=NVARCHAR},
          </if>
          <if test="record.success != null">
            SUCCESS = #{record.success,jdbcType=VARCHAR},
          </if>
          <if test="record.result != null">
            RESULT = #{record.result,jdbcType=NVARCHAR},
          </if>
          <if test="record.createtime != null">
            CREATETIME = #{record.createtime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.responsetime != null">
            RESPONSETIME = #{record.responsetime,jdbcType=FLOAT},
          </if>
        </trim>
      </otherwise>
    </choose>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update T_LOG_RISKIMAGE
    set SERIALNO = #{record.serialno,jdbcType=NVARCHAR},
    RPFSERIALNO = #{record.rpfserialno,jdbcType=NVARCHAR},
    CERTID = #{record.certid,jdbcType=NVARCHAR},
    CUSTOMER_NO = #{record.customerNo,jdbcType=NVARCHAR},
    CUSTOMERNAME = #{record.customername,jdbcType=NVARCHAR},
    MOBILE_PROV_NM = #{record.mobileProvNm,jdbcType=NVARCHAR},
    ID_NO_PROV_NM = #{record.idNoProvNm,jdbcType=NVARCHAR},
    LIVE_PROVINCE = #{record.liveProvince,jdbcType=NVARCHAR},
    IP_PROV_CN = #{record.ipProvCn,jdbcType=NVARCHAR},
    SUCCESS = #{record.success,jdbcType=VARCHAR},
    RESULT = #{record.result,jdbcType=NVARCHAR},
    CREATETIME = #{record.createtime,jdbcType=TIMESTAMP},
    RESPONSETIME = #{record.responsetime,jdbcType=FLOAT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="map">
    update T_LOG_RISKIMAGE
    SET
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName} = #{record.${column.javaProperty},jdbcType=${column.jdbcType}}
        </foreach>
      </when>
      <otherwise>
        <trim suffixOverrides=",">
          <if test="record.rpfserialno != null">
            RPFSERIALNO = #{record.rpfserialno,jdbcType=NVARCHAR},
          </if>
          <if test="record.certid != null">
            CERTID = #{record.certid,jdbcType=NVARCHAR},
          </if>
          <if test="record.customerNo != null">
            CUSTOMER_NO = #{record.customerNo,jdbcType=NVARCHAR},
          </if>
          <if test="record.customername != null">
            CUSTOMERNAME = #{record.customername,jdbcType=NVARCHAR},
          </if>
          <if test="record.mobileProvNm != null">
            MOBILE_PROV_NM = #{record.mobileProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.idNoProvNm != null">
            ID_NO_PROV_NM = #{record.idNoProvNm,jdbcType=NVARCHAR},
          </if>
          <if test="record.liveProvince != null">
            LIVE_PROVINCE = #{record.liveProvince,jdbcType=NVARCHAR},
          </if>
          <if test="record.ipProvCn != null">
            IP_PROV_CN = #{record.ipProvCn,jdbcType=NVARCHAR},
          </if>
          <if test="record.success != null">
            SUCCESS = #{record.success,jdbcType=VARCHAR},
          </if>
          <if test="record.result != null">
            RESULT = #{record.result,jdbcType=NVARCHAR},
          </if>
          <if test="record.createtime != null">
            CREATETIME = #{record.createtime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.responsetime != null">
            RESPONSETIME = #{record.responsetime,jdbcType=FLOAT},
          </if>
        </trim>
      </otherwise>
    </choose>
    where SERIALNO = #{record.serialno,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO">
    update T_LOG_RISKIMAGE
    set RPFSERIALNO    = #{rpfserialno,jdbcType=NVARCHAR},
        CERTID         = #{certid,jdbcType=NVARCHAR},
        CUSTOMER_NO    = #{customerNo,jdbcType=NVARCHAR},
        CUSTOMERNAME   = #{customername,jdbcType=NVARCHAR},
        MOBILE_PROV_NM = #{mobileProvNm,jdbcType=NVARCHAR},
        ID_NO_PROV_NM  = #{idNoProvNm,jdbcType=NVARCHAR},
        LIVE_PROVINCE  = #{liveProvince,jdbcType=NVARCHAR},
        IP_PROV_CN     = #{ipProvCn,jdbcType=NVARCHAR},
        SUCCESS        = #{success,jdbcType=VARCHAR},
        RESULT         = #{result,jdbcType=NVARCHAR},
        CREATETIME     = #{createtime,jdbcType=TIMESTAMP},
        RESPONSETIME   = #{responsetime,jdbcType=FLOAT}
    where SERIALNO = #{serialno,jdbcType=NVARCHAR}
  </update>
  <select id="selectOneByExample" parameterType="com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogQuery"
    resultMap="BaseResultMap">
    select
    'true' as QUERYID,
    <include refid="Base_Column_List"/>
    from T_LOG_RISKIMAGE
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    select
    'true' as QUERYID,
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List"/>
      </otherwise>
    </choose>
    from T_LOG_RISKIMAGE
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>