# 该配置文件为产品内置，如需覆盖请将对应的kv对放入对客配置

# 模型配置
model:
  # 模型节点全局默认配置
  global-default-config:
    # 节点并发限制
    max-concurrency: 1
    # 故障转移节点优先级
    priority: 100
    # 故障转移初始权重
    init-weight: 20
    # 故障转移最大权重
    max-weight: 100
    # 故障转移最小权重
    min-weight: 0
    # 先觉服务Token
    authorization:
  # 故障转移策略
  failover:
    # 是否启用并发控制
    enable-concurrency-control: true
    # 是否立即启用健康检查任务
    enable-check-task-immediately: true
    # 健康检查任务间隔（毫秒）
    check-task-interval: 3000
    # 调用失败后权重降低倍率（乘以当前权重）
    fail-keep-rate-of-current-weight: 0.5
    # 调用成功后权重上升倍率（乘以最大权重）
    success-increase-rate-of-max-weight: 0.05
    # 健康检查连续成功阈值（需要N次连续健康检查成功才开始恢复节点权重）
    recover-threshold: 3
    # 节点下线阈值（调用失败后权重低于该值时直接置为最低权重）
    down-threshold: 20
# redis配置
redis:
  # 全局前缀
  global-prefix: risk_image_
  # 分布式组件自动解锁时间（毫秒）
  lease-time: 5000
  # set组件前缀
  set-prefix: set_
  # map组件前缀
  map-prefix: map_
  # lock组件前缀
  lock-prefix: lock_
  # queue组件前缀
  queue-prefix: queue_
  # semaphore组件前缀
  semaphore-prefix: semaphore_
  # readWriteLock组件前缀
  read-write-lock-prefix: rwlock_
  # fairSemaphore组件lock子组件前缀
  fair-semaphore-lock-prefix: fair_semaphore_lock_
  # fairSemaphore组件semaphore子组件前缀
  fair-semaphore-semaphore-prefix: fair_semaphore_semaphore_
# oracle数据库配置
database:
  # 是否启用响应结果落库
  enable: false
  # 连接池最大连接数
  max-pool-size: 20
  # 连接池名称
  pool-name: gateway-datasource-
  # 数据库驱动
  driver-class-name: oracle.jdbc.
# 特征向量引擎配置
vector-engine:
  # 特征向量库名称前缀
  prefix: bestpay_risk_image_
  # 默认模式
  mode: DEFAULT
  # 默认分组权重
  default-weight: 100
# 网关配置
gateway:
  # 加密配置
  encrypt:
    # blowfish密钥
    blow-fish-key: NGVmZDA5NjEzNzZmNGMxMDk5ZjFmNDZkOWI0NTg3MDk=
  # 线程池配置
  executor:
    # 最大线程数
    max-pool-size: 300
    # 最小线程数
    core-pool-size: 60
    # 队列容量
    queue-capacity: 600
    # 空闲线程存活时间（秒）
    keep-alive-seconds: 30
    # 线程名前缀
    thread-name-prefix: gateway-executor-
    # 是否允许核心线程超时后回收
    allow-core-thread-timeout: true
  # http配置
  http:
    # 读取超时时间（毫秒）
    read-timeout: 6000
    # 连接超时时间（毫秒）
    connect-timeout: 2000
    # 连接池连接数
    connection-pool-size: 64
    # 连接池存活时间（毫秒）
    connection-pool-time-to-live: 120000
    # 连接池等待时间（毫秒）
    connection-checkout-timeout: 2000
    # 是否启用自动重试
    enable-automatic-retries: true
    # 是否启用TLS证书校验
    enable-trust-manager: true
  # 暂未使用
  load-balance:
    enable-fast-fail-on-high-load: false
    high-load-threshold: 0.8
    low-load-threshold: 0.3
    avg-time-cost: 500
    expected-timeout: 3000
  # 背景照服务配置
  risk-image:
    # 数据库批量落库单批次最大数量
    max-db-insert-batch-size: 1000
    # 数据库批量落库队列名称
    db-log-queue-name: db_log_queue
    # 背景照特征向量库名称后缀
    background-collection-suffix: _background
    # 自定义场景特征向量库名称后缀
    scene-collection-suffix: _scene
    # 黑样本特征向量库名称后缀
    black-collection-suffix: _black
    # 暂未使用
    enable-default-collection: true
    # 暂未使用
    default-collection-name: risk_image_default
    # 自定义场景特征向量库默认阈值
    default-scene-threshold: 0.5
    # 黑样本特征向量库默认阈值
    default-black-threshold: 0.5
    # 暂未使用
    default-shards-num: 2
    # redis缓存读写锁自动解锁时间（毫秒）
    cache-lock-lease-time: 10000
    # redis缓存重建延迟时间（毫秒）
    cache-rebuild-delay: 1000
    # 是否启用特征向量向量归一化
    enable-vector-normalization: false
    # 特征向量维度
    vectors-dim: 768
    # 特征向量库默认最大特征向量数量
    default-max-vectors-num: 10_0000
    # 搜索引擎默认最大搜索响应特征特征向量数量
    max-search-size: 10000
    # 背景照鉴伪业务流程所需最少特征向量数量
    min-vectors-num: 50
    # 特征向量搜索候选数
    num-candidates: 100
    # 背景照鉴伪业务流程搜索特征向量数量
    top-k: 10
    # 背景照鉴伪业务流程搜索响应所需最少特征向量数量
    min-k: 5


