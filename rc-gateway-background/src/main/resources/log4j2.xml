<?xml version="1.0" encoding="UTF-8"?>
<Configuration monitorInterval="600" xmlns:xi="http://www.w3.org/2001/XInclude" status="INFO">
  <Properties>
    <Property name="logDirect">/data/appLogs</Property>
    <Property name="logFileName">rc-gateway-background</Property>
    <Property name="loggerLogLevel">INFO</Property>
  </Properties>
  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
      <PatternLayout>
        <MarkerPatternSelector defaultPattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%logger %line][%method]%message%n">
          <PatternMatch key="PARAMETER"
            pattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%mdc{CLASS_NAME} %mdc{METHOD_LINE}][%mdc{METHOD_NAME}]%message%n"/>
          <PatternMatch key="RESPONSE"
            pattern="[%d{DEFAULT}][%level][%threadName][%mdc{TRACE_LOG_ID}][%mdc{CLASS_NAME} %mdc{METHOD_LINE}][%mdc{METHOD_NAME}][%mdc{RESPONSE_CODE}][%mdc{RESPONSE_STATUS}][%mdc{ELAPSED_TIME}]%message%n"/>
        </MarkerPatternSelector>
      </PatternLayout>
    </Console>
    <xi:include href="log4j2-router.xml"/>
  </Appenders>
  <Loggers>
    <Root level="${loggerLogLevel}" includeLocation="true">
      <!--<AppenderRef ref="Console"/>-->
      <AppenderRef ref="router"/>
    </Root>
  </Loggers>
</Configuration>