package com.bestpay.media.platform.rc.gateway.background.service.cache;

import lombok.Builder;
import lombok.Data;
import org.redisson.api.RBatch;
import org.redisson.api.RMapAsync;
import org.redisson.api.RSetAsync;

/**
 * @Author: 段然
 * @Date: 2024/9/12
 */
@Data
@Builder
public class ProductCacheBatch {

    private final RBatch batch;
    private final RSetAsync<String> vectorIdSet;
    private final RMapAsync<String, Object> metaMap;
    private final RMapAsync<String, Long> vectorCounter;
    private final RMapAsync<String, Long> idProvinceCounter;
    private final RMapAsync<String, Long> ipProvinceCounter;
    private final RMapAsync<String, Long> liveProvinceCounter;
    private final RMapAsync<String, Long> mobileProvinceCounter;

}
