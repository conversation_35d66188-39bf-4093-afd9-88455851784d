package com.bestpay.media.platform.rc.gateway.background.controller.impl;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_BACKGROUND_COUNT;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.SEARCH_FILTER_KEY_LIVE_PROVINCE;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_DUPLICATE_CUSTOMER_NO;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_DUPLICATE_RPF_SERIAL_NO;

import com.bestpay.media.platform.rc.gateway.background.component.vector.BackgroundVector;
import com.bestpay.media.platform.rc.gateway.background.component.vector.BlackVector;
import com.bestpay.media.platform.rc.gateway.background.component.vector.SceneVector;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.controller.BackgroundController;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO.RiskImageData;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO.CalculateData;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO.CalculateData.CalculateProvinceInfo;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO.CalculateData.CalculateVector;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.background.service.ProductCacheService;
import com.bestpay.media.platform.rc.gateway.background.service.RiskImageService;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCacheInfo;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.util.ResponseUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 段然
 * @Date: 2024/4/7
 */
@Slf4j
@Filter
@RestController
public class BackgroundControllerImpl implements BackgroundController {

    private final Executor executor;
    private final RiskImageConfig config;
    private final RedisManager redisManager;
    private final CollectionUtil collectionUtil;
    private final RiskImageService riskImageService;
    private final ProductCacheService productCacheService;
    private final VectorEngineServiceProvider serviceProvider;

    public BackgroundControllerImpl(Executor executor, RiskImageConfig config, RedisManager redisManager, CollectionUtil collectionUtil,
        RiskImageService riskImageService, ProductCacheService productCacheService, VectorEngineServiceProvider serviceProvider) {
        this.config = config;
        this.executor = executor;
        this.redisManager = redisManager;
        this.collectionUtil = collectionUtil;
        this.serviceProvider = serviceProvider;
        this.riskImageService = riskImageService;
        this.productCacheService = productCacheService;
    }

    @Override
    public Response<String> riskImage(RiskImageRequestDTO request) {

        // 1. 判断产品是否存在，不存在直接返回
        RiskImageData requestData = request.getData();
        String productId = requestData.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();
        ProductCacheInfo cacheInfo = productCache.getProductCacheInfo();

        // 2. 判断rpfserialno是否重复，重复直接返回
        if (cacheInfo.getRequestIdSet().contains(requestData.getRpfserialno())) {
            return ResponseUtil.onError(INVALID_PARAM_DUPLICATE_RPF_SERIAL_NO);
        }

        // 3. 提取特征，并将当前请求异步插入背景照库
        boolean featureOnly = Boolean.parseBoolean(request.getData().getFeature_only());
        ExtractResult extracted = !featureOnly ? riskImageService.extract(requestData.getBase64(), requestData.getCustomer_no()) :
            ExtractResult.builder()
                .img_feature(requestData.getImg_feature())
                .noise(requestData.getNoise())
                .build();

        BackgroundVector vector = BackgroundVector.builder()
            .id(requestData.getRpfserialno())
            .vector(extracted.getImg_feature())
            .noise(extracted.getNoise())
            .creation_date(System.currentTimeMillis())
            .customer_no(requestData.getCustomer_no())
            .customer_name(requestData.getCustomername())
            .rpf_serial_no(requestData.getRpfserialno())
            .cert_id(requestData.getCertid())
            .mobile_prov_nm(Strings.nullToEmpty(requestData.getMobile_prov_nm()))
            .id_no_prov_nm(Strings.nullToEmpty(requestData.getId_no_prov_nm()))
            .live_province(Strings.nullToEmpty(requestData.getLive_province()))
            .ip_prov_cn(Strings.nullToEmpty(requestData.getIp_prov_cn()))
            .geo_info(requestData.getGeo_info())
            .product_id(productId)
            .build();

        SearchParam searchParam = SearchParam.builder()
            .vector(vector.getVector())
            .numCandidates(config.getNumCandidates())
            .topK(config.getTopK())
            .build();

        VectorService<BackgroundVector> backgroundVectorService = serviceProvider.getVectorService(
            collectionUtil.buildBackgroundCollectionName(productId), BackgroundVector.class);
        VectorService<SceneVector> sceneVectorService = serviceProvider.getVectorService(
            collectionUtil.buildSceneCollectionName(productId), SceneVector.class);
        VectorService<BlackVector> blackVectorService = serviceProvider.getVectorService(
            collectionUtil.buildBlackCollectionName(productId), BlackVector.class);

        executor.execute(() -> {
            try {
                backgroundVectorService.insertVector(vector);
                productCache.addBackground(vector);
            } catch (Exception e) {
                log.error("插入背景照失败", e);
            }
        });

        // 4. 判断是否insertOnly，是则直接返回
        boolean insertOnly = Boolean.parseBoolean(request.getData().getInsert_only());
        if (insertOnly) {
            return ResponseUtil.success(null,
                "当insert_only为true时，只插入背景照，不进行计算");
        }

//        // 5. 搜索自定义场景库，命中则直接返回
//        long currentSceneVectorSize = cacheInfo.getVectorCounter().getOrDefault(CACHE_KEY_SCENE_COUNT, 0L);
//        if (currentSceneVectorSize <= 0) {
//            log.info("当前自定义场景照数量为{}，跳过搜索", currentSceneVectorSize);
//        } else {
//            List<SearchResult<SceneVector>> sceneSearchResults = sceneVectorService.searchVector(searchParam);
//            if (!sceneSearchResults.isEmpty()) {
//                List<SearchResult<SceneVector>> matchedResult = sceneSearchResults.stream()
//                    .filter(sceneSearchResult -> {
//                        Double score = sceneSearchResult.getScore();
//                        double threshold = sceneSearchResult.getVector().getThreshold() == null ? config.getDefaultSceneThreshold()
//                            : sceneSearchResult.getVector().getThreshold();
//                        return score != null && score > threshold;
//                    })
//                    .collect(Collectors.toList());
//                if (!matchedResult.isEmpty()) {
//                    String matchedInfo = StringUtils.join(matchedResult.stream()
//                        .map(sceneSearchResult ->
//                            sceneSearchResult.getVector().getName() + ":" + sceneSearchResult.getScore()).iterator(), ';');
//                    return ResponseUtil.success(null, "命中自定义场景：" + matchedInfo);
//                }
//            }
//        }
//
//        //6. 搜索黑样本库，命中则直接返回
//        long currentBlackVectorSize = cacheInfo.getVectorCounter().getOrDefault(CACHE_KEY_BLACK_COUNT, 0L);
//        if (currentBlackVectorSize <= 0) {
//            log.info("当前黑样本数量为{}，跳过搜索", currentBlackVectorSize);
//        } else {
//            List<SearchResult<BlackVector>> blackSearchResults = blackVectorService.searchVector(searchParam);
//            if (!blackSearchResults.isEmpty()) {
//                List<SearchResult<BlackVector>> matchedResult = blackSearchResults.stream()
//                    .filter(blackSearchResult -> {
//                        Double score = blackSearchResult.getScore();
//                        double threshold = blackSearchResult.getVector().getThreshold() == null ? config.getDefaultBlackThreshold()
//                            : blackSearchResult.getVector().getThreshold();
//                        return score != null && score > threshold;
//                    })
//                    .collect(Collectors.toList());
//                if (!matchedResult.isEmpty()) {
//                    String matchedInfo = StringUtils.join(matchedResult.stream()
//                        .map(vectorSearchResult ->
//                            vectorSearchResult.getVector().getId() + ":" + vectorSearchResult.getScore()).iterator(), ';');
//                    String outputInfo = StringUtils.join(matchedResult.stream()
//                        .map(vectorSearchResult ->
//                            vectorSearchResult.getVector().getId()).iterator(), '_');
//                    log.info("命中黑样本：{}", matchedInfo);
//                    return ResponseUtil.success(null, outputInfo);
//                }
//            }
//        }

        // 7. 判断背景照库数据量是否满足最低需求
        long currentBackgroundVectorSize = cacheInfo.getVectorCounter().getOrDefault(CACHE_KEY_BACKGROUND_COUNT, 0L);
        boolean notEnoughData = currentBackgroundVectorSize < config.getMinVectorsNum();
        if (notEnoughData) {
            return ResponseUtil.success(null,
                "当前背景照数量为" + currentBackgroundVectorSize + "，数据量低于最低要求" + config.getMinVectorsNum() + "，不进行计算");
        }

        // 8. 搜索背景照库
        List<SearchResult<BackgroundVector>> searchResults = backgroundVectorService.searchVector(searchParam);
        if (searchResults.size() <= config.getMinK()) {
            return ResponseUtil.success(null, "背景照搜索响应数量低于最低要求" + config.getMinK());
        }
        SearchResult<BackgroundVector> currentVectorResult = SearchResult.<BackgroundVector>builder()
            .score(1.0)
            .vector(vector)
            .build();
        searchResults.add(currentVectorResult);
        searchResults.sort(Comparator.comparingDouble(SearchResult<BackgroundVector>::getScore).reversed());

        // 9. 调用计算接口
        CalculateData calculateData = CalculateData.builder()
            .province_info(CalculateProvinceInfo.builder()
                .all_search_num(currentBackgroundVectorSize)
                .id_no_prov_nm(Strings.isNullOrEmpty(vector.getId_no_prov_nm()) ? -1
                    : cacheInfo.getIdProvinceCounter().getOrDefault(vector.getId_no_prov_nm(), -1L))
                .mobile_prov_nm(Strings.isNullOrEmpty(vector.getMobile_prov_nm()) ? -1
                    : cacheInfo.getMobileProvinceCounter().getOrDefault(vector.getMobile_prov_nm(), -1L))
                .live_province(Strings.isNullOrEmpty(vector.getLive_province()) ? -1
                    : cacheInfo.getLiveProvinceCounter().getOrDefault(vector.getLive_province(), -1L))
                .ip_prov_cn(Strings.isNullOrEmpty(vector.getIp_prov_cn()) ? -1
                    : cacheInfo.getIpProvinceCounter().getOrDefault(vector.getIp_prov_cn(), -1L))
                .build())
            .vectors_background(searchResults.stream()
                .map(searchResult -> CalculateVector.builder()
                    .rpfserialno(searchResult.getVector().getRpf_serial_no())
                    .customer_no(searchResult.getVector().getCustomer_no())
                    .customername(searchResult.getVector().getCustomer_name())
                    .certid(searchResult.getVector().getCert_id())
                    .feature(searchResult.getVector().getVector())
                    .noise(searchResult.getVector().getNoise())
                    .score(searchResult.getScore())
                    .mobile_prov_nm(searchResult.getVector().getMobile_prov_nm())
                    .id_no_prov_nm(searchResult.getVector().getId_no_prov_nm())
                    .live_province(searchResult.getVector().getLive_province())
                    .ip_prov_cn(searchResult.getVector().getIp_prov_cn())
                    .geo_info(searchResult.getVector().getGeo_info())
                    .build())
                .collect(Collectors.toList()))
            .build();
        String calculateResult = riskImageService.calculate(calculateData);

        // 10. 返回数据，响应结果异步落数据库
        return ResponseUtil.success(calculateResult);
    }

    @Override
    public Response<AnalysisResponseDTO> analysis(AnalysisRequestDTO request) {
        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();
        ProductCacheInfo cacheInfo = productCache.getProductCacheInfo();
        return ResponseUtil.success(AnalysisResponseDTO.builder()
            .idNoProvInfo(Maps.newHashMap(cacheInfo.getIdProvinceCounter()))
            .ipProvCnInfo(Maps.newHashMap(cacheInfo.getIpProvinceCounter()))
            .liveProvInfo(Maps.newHashMap(cacheInfo.getLiveProvinceCounter()))
            .mobileProvInfo(Maps.newHashMap(cacheInfo.getMobileProvinceCounter()))
            .build());
    }

    @Override
    public Response<List<SearchBackgroundResponseDTO>> searchImage(SearchBackgroundRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildBackgroundCollectionName(productId);
        VectorService<BackgroundVector> vectorService = serviceProvider.getVectorService(collectionName, BackgroundVector.class);

        ExtractResult extracted = riskImageService.extract(request.getBase64(),request.getRequestId());

        SearchParam searchParam = SearchParam.builder()
            .topK(request.getTopN())
            .numCandidates(config.getNumCandidates())
            .vector(extracted.getImg_feature())
            .build();

        if (!Strings.isNullOrEmpty(request.getLiveProvince())) {
            searchParam.setFilter(Collections.singletonMap(SEARCH_FILTER_KEY_LIVE_PROVINCE, request.getLiveProvince()));
        }

        List<SearchResult<BackgroundVector>> searchResults = vectorService.searchVector(searchParam);

        List<SearchBackgroundResponseDTO> response = searchResults.isEmpty() ? Collections.emptyList() :
            searchResults.stream()
                .filter(result -> result.getScore() >= request.getThreshold())
                .map(result ->
                    SearchBackgroundResponseDTO.builder()
                        .id(result.getVector().getId())
                        .rpfserialno(result.getVector().getRpf_serial_no())
                        .certid(result.getVector().getCert_id())
                        .customername(result.getVector().getCustomer_name())
                        .customer_no(result.getVector().getCustomer_no())
                        .mobile_prov_nm(result.getVector().getMobile_prov_nm())
                        .id_no_prov_nm(result.getVector().getId_no_prov_nm())
                        .live_province(result.getVector().getLive_province())
                        .ip_prov_cn(result.getVector().getIp_prov_cn())
                        .productId(result.getVector().getProduct_id())
                        .score(result.getScore())
                        .build())
                .sorted(Comparator.comparing(SearchBackgroundResponseDTO::getScore).reversed())
                .collect(Collectors.toList());

        return ResponseUtil.success(response);
    }
}

