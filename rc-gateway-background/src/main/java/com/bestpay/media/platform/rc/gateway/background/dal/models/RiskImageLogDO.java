package com.bestpay.media.platform.rc.gateway.background.dal.models;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021-8-8 10:30:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskImageLogDO {

    private String serialno;

    private String rpfserialno;

    private String certid;

    private String customerNo;

    private String customername;

    private String mobileProvNm;

    private String idNoProvNm;

    private String liveProvince;

    private String ipProvCn;

    private String success;

    private String result;

    private Date createtime;

    private Double responsetime;

    public static RiskImageLogDO build(RiskImageRequestDTO request,
        Response<String> response, Date createTime) {
        RiskImageLogDO riskImageLogDO = new RiskImageLogDO();
        if (request != null && request.getData() != null) {
            riskImageLogDO.setRpfserialno(Strings.nullToEmpty(request.getData().getRpfserialno()));
            riskImageLogDO.setCertid(Strings.nullToEmpty(request.getData().getCertid()));
            riskImageLogDO.setCustomerNo(Strings.nullToEmpty(request.getData().getCustomer_no()));
            riskImageLogDO.setCustomername(Strings.nullToEmpty(request.getData().getCustomername()));
            riskImageLogDO.setMobileProvNm(Strings.nullToEmpty(request.getData().getMobile_prov_nm()));
            riskImageLogDO.setIdNoProvNm(Strings.nullToEmpty(request.getData().getId_no_prov_nm()));
            riskImageLogDO.setLiveProvince(Strings.nullToEmpty(request.getData().getLive_province()));
            riskImageLogDO.setIpProvCn(Strings.nullToEmpty(request.getData().getIp_prov_cn()));
        }
        riskImageLogDO.setSuccess(response.getSuccess() == null ? "" : response.getSuccess().toString());
        riskImageLogDO.setResult(Strings.nullToEmpty(response.getResult()));
        riskImageLogDO.setResponsetime(response.getElapsed() / 1000.0);
        riskImageLogDO.setCreatetime(createTime);
        return riskImageLogDO;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName()
            + " ["
            + "Hash = " + hashCode()
            + ", serialno=" + serialno
            + ", rpfserialno=" + rpfserialno
            + ", certid=" + certid
            + ", customerNo=" + customerNo
            + ", customername=" + customername
            + ", mobileProvNm=" + mobileProvNm
            + ", idNoProvNm=" + idNoProvNm
            + ", liveProvince=" + liveProvince
            + ", ipProvCn=" + ipProvCn
            + ", success=" + success
            + ", result=" + result
            + ", createtime=" + createtime
            + ", responsetime=" + responsetime
            + "]";
    }

    public enum Column {
        /**
         * serialno
         */
        serialno("SERIALNO", "serialno", "NVARCHAR", false),
        /**
         * rpfserialno
         */
        rpfserialno("RPFSERIALNO", "rpfserialno", "NVARCHAR", false),
        /**
         * certid
         */
        certid("CERTID", "certid", "NVARCHAR", false),
        /**
         * customerNo
         */
        customerNo("CUSTOMER_NO", "customerNo", "NVARCHAR", false),
        /**
         * customername
         */
        customername("CUSTOMERNAME", "customername", "NVARCHAR", false),
        /**
         * mobileProvNm
         */
        mobileProvNm("MOBILE_PROV_NM", "mobileProvNm", "NVARCHAR", false),
        /**
         * idNoProvNm
         */
        idNoProvNm("ID_NO_PROV_NM", "idNoProvNm", "NVARCHAR", false),
        /**
         * liveProvince
         */
        liveProvince("LIVE_PROVINCE", "liveProvince", "NVARCHAR", false),
        /**
         * ipProvCn
         */
        ipProvCn("IP_PROV_CN", "ipProvCn", "NVARCHAR", false),
        /**
         * success
         */
        success("SUCCESS", "success", "VARCHAR", false),
        /**
         * result
         */
        result("RESULT", "result", "NVARCHAR", false),
        /**
         * createtime
         */
        createtime("CREATETIME", "createtime", "TIMESTAMP", false),
        /**
         * responsetime
         */
        responsetime("RESPONSETIME", "responsetime", "FLOAT", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean getColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean getColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.getColumnNameDelimited = getColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.getColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column)
                    .append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}