package com.bestpay.media.platform.rc.gateway.background.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2021/1/13
 */
@Data
@ToString
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CalculateRequestDTO implements Serializable {

    private CalculateData data;


    @Data
    @ToString
    @SuperBuilder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CalculateData implements Serializable {

        private final Integer index = 1;

        private List<CalculateVector> vectors_background;

        private CalculateProvinceInfo province_info;

        @Data
        @ToString
        @SuperBuilder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class CalculateVector {

            private String rpfserialno;

            private String customer_no;

            private String customername;

            private String certid;

            private List<Float> feature;

            private String noise;

            private Double score;

            private String live_province;

            private String id_no_prov_nm;

            private String mobile_prov_nm;

            private String ip_prov_cn;

            private String geo_info;

        }

        @Data
        @ToString
        @SuperBuilder
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class CalculateProvinceInfo {

            private Long all_search_num;

            private Long live_province;

            private Long id_no_prov_nm;

            private Long mobile_prov_nm;

            private Long ip_prov_cn;

        }
    }
}
