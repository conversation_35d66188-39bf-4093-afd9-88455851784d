package com.bestpay.media.platform.rc.gateway.background.controller.entity.product;

import com.bestpay.media.platform.rc.gateway.common.entity.Request;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateProductRequestDTO extends Request {

    @NotBlank
    @Size(min = 4, max = 64)
    @Pattern(regexp = "^[a-z0-9_]+$", message = "productId只能由小写字母、数字、下划线组成")
    private String productId;

    @Min(100)
    @Max(300_0000)
    private Long maxCollectionSize;

    @Size(max = 128)
    private String description;
}
