package com.bestpay.media.platform.rc.gateway.background.model;

import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.HealthcheckRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.HealthcheckResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

/**
 * <AUTHOR> 2021/1/11
 */
@Path("/")
@Consumes("application/json")
@Produces("application/json")
public interface RiskImageModelService extends ModelService {

    @POST
    ExtractResponseDTO extract(ExtractRequestDTO request);

    @POST
    CalculateResponseDTO calculate(CalculateRequestDTO request);

    @POST
    HealthcheckResponseDTO healthCheck(HealthcheckRequestDTO request);

}
