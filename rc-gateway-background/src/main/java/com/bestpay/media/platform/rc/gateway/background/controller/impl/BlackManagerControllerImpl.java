package com.bestpay.media.platform.rc.gateway.background.controller.impl;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.SEARCH_FILTER_KEY_LIVE_PROVINCE;

import com.bestpay.media.platform.rc.gateway.background.component.vector.BlackVector;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.controller.BlackManageController;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.AddBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.DeleteBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.background.service.ProductCacheService;
import com.bestpay.media.platform.rc.gateway.background.service.RiskImageService;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.util.ResponseUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import com.google.common.base.Strings;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Author: 段然
 * @Date: 2024/9/11
 */
@Slf4j
@Filter
@Validated
@RestController
public class BlackManagerControllerImpl implements BlackManageController {

    private final RiskImageConfig config;
    private final CollectionUtil collectionUtil;
    private final RiskImageService riskImageService;
    private final ProductCacheService productCacheService;
    private final VectorEngineServiceProvider serviceProvider;

    public BlackManagerControllerImpl(CollectionUtil collectionUtil, RiskImageConfig config, RiskImageService riskImageService,
        ProductCacheService productCacheService, VectorEngineServiceProvider serviceProvider) {
        this.config = config;
        this.collectionUtil = collectionUtil;
        this.serviceProvider = serviceProvider;
        this.riskImageService = riskImageService;
        this.productCacheService = productCacheService;
    }

    @Override
    public Response<String> addBlack(AddBlackRequestDTO request) {
        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        ExtractResult extracted = riskImageService.extract(request.getBase64(), request.getId());

        String collectionName = collectionUtil.buildBlackCollectionName(productId);
        VectorService<BlackVector> vectorService = serviceProvider.getVectorService(collectionName, BlackVector.class);

        BlackVector vector = BlackVector.builder()
            .id(request.getId())
            .threshold(ObjectUtils.firstNonNull(request.getThreshold(), config.getDefaultBlackThreshold()))
            .vector(extracted.getImg_feature())
            .noise(extracted.getNoise())
            .live_province(request.getLiveProvince())
            .creation_date(System.currentTimeMillis())
            .product_id(productId)
            .build();

        String insertedId = vectorService.insertVector(vector);

        productCache.addBlack();

        return ResponseUtil.success(insertedId);
    }

    @Override
    public Response<Boolean> deleteBlack(DeleteBlackRequestDTO request) {
        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildBlackCollectionName(productId);
        VectorService<BlackVector> vectorService = serviceProvider.getVectorService(collectionName, BlackVector.class);

        boolean deleted = vectorService.deleteVectorById(request.getId());

        productCache.deleteBlack();

        return ResponseUtil.success(deleted);
    }

    @Override
    public Response<List<QueryBlackResponseDTO>> queryBlack(QueryBlackRequestDTO request) {
        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildBlackCollectionName(productId);
        VectorService<BlackVector> vectorService = serviceProvider.getVectorService(collectionName, BlackVector.class);

        // TODO 添加查询分页
        List<BlackVector> vectors = vectorService.queryVectorList(0, config.getMaxSearchSize());

        List<QueryBlackResponseDTO> response = vectors.isEmpty() ? Collections.emptyList() :
            vectors.stream()
                .map(result ->
                    QueryBlackResponseDTO.builder()
                        .id(result.getId())
                        .threshold(result.getThreshold())
                        .productId(result.getProduct_id())
                        .liveProvince(result.getLive_province())
                        .build())
                .collect(Collectors.toList());

        return ResponseUtil.success(response);
    }

    @Override
    public Response<List<SearchBlackResponseDTO>> searchBlack(SearchBlackRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildBlackCollectionName(productId);
        VectorService<BlackVector> vectorService = serviceProvider.getVectorService(collectionName, BlackVector.class);

        ExtractResult extracted = riskImageService.extract(request.getBase64(), request.getRequestId());

        SearchParam searchParam = SearchParam.builder()
            .topK(request.getTopN())
            .numCandidates(config.getNumCandidates())
            .vector(extracted.getImg_feature())
            .build();

        if (!Strings.isNullOrEmpty(request.getLiveProvince())) {
            searchParam.setFilter(Collections.singletonMap(SEARCH_FILTER_KEY_LIVE_PROVINCE, request.getLiveProvince()));
        }

        List<SearchResult<BlackVector>> searchResults = vectorService.searchVector(searchParam);

        List<SearchBlackResponseDTO> response = searchResults.isEmpty() ? Collections.emptyList() :
            searchResults.stream()
                .filter(result -> result.getScore() >= request.getThreshold())
                .map(result ->
                    SearchBlackResponseDTO.builder()
                        .id(result.getVector().getId())
                        .threshold(result.getVector().getThreshold())
                        .productId(result.getVector().getProduct_id())
                        .liveProvince(result.getVector().getLive_province())
                        .score(result.getScore())
                        .build())
                .sorted(Comparator.comparing(SearchBlackResponseDTO::getScore).reversed())
                .collect(Collectors.toList());

        return ResponseUtil.success(response);
    }
}
