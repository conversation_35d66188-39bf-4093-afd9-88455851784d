package com.bestpay.media.platform.rc.gateway.background.controller;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CONTENT_TYPE_JSON;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.AddBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.DeleteBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import java.util.List;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> 2021/1/11
 */
@Validated
@RequestMapping(produces = CONTENT_TYPE_JSON, consumes = CONTENT_TYPE_JSON)
public interface BlackManageController {

    @RequestMapping(value = "/addBlack", method = POST)
    Response<String> addBlack(@RequestBody @Valid AddBlackRequestDTO request);

    @RequestMapping(value = "/deleteBlack", method = POST)
    Response<Boolean> deleteBlack(@RequestBody @Valid DeleteBlackRequestDTO request);

    @RequestMapping(value = "/queryBlack", method = POST)
    Response<List<QueryBlackResponseDTO>> queryBlack(@RequestBody @Valid QueryBlackRequestDTO request);

    @RequestMapping(value = "/searchBlack", method = POST)
    Response<List<SearchBlackResponseDTO>> searchBlack(@RequestBody @Valid SearchBlackRequestDTO request);

}
