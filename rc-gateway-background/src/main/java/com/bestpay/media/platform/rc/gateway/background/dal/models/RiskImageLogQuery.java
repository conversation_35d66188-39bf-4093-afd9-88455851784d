package com.bestpay.media.platform.rc.gateway.background.dal.models;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RiskImageLogQuery {

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RiskImageLogQuery() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public RiskImageLogQuery orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public RiskImageLogQuery orderBy(String... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public static Criteria newAndCreateCriteria() {
        RiskImageLogQuery example = new RiskImageLogQuery();
        return example.createCriteria();
    }

    public RiskImageLogQuery when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public RiskImageLogQuery when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * <AUTHOR>
     * @Date 2021-8-8 10:30:05
     */
    protected static class GeneratedCriteria {

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSerialnoIsNull() {
            addCriterion("SERIALNO is null");
            return (Criteria) this;
        }

        public Criteria andSerialnoIsNotNull() {
            addCriterion("SERIALNO is not null");
            return (Criteria) this;
        }

        public Criteria andSerialnoEqualTo(String value) {
            addCriterion("SERIALNO =", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoNotEqualTo(String value) {
            addCriterion("SERIALNO <>", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThan(String value) {
            addCriterion("SERIALNO >", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThanOrEqualTo(String value) {
            addCriterion("SERIALNO >=", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThan(String value) {
            addCriterion("SERIALNO <", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThanOrEqualTo(String value) {
            addCriterion("SERIALNO <=", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SERIALNO <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSerialnoLike(String value) {
            addCriterion("SERIALNO like", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotLike(String value) {
            addCriterion("SERIALNO not like", value, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoIn(List<String> values) {
            addCriterion("SERIALNO in", values, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotIn(List<String> values) {
            addCriterion("SERIALNO not in", values, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoBetween(String value1, String value2) {
            addCriterion("SERIALNO between", value1, value2, "serialno");
            return (Criteria) this;
        }

        public Criteria andSerialnoNotBetween(String value1, String value2) {
            addCriterion("SERIALNO not between", value1, value2, "serialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoIsNull() {
            addCriterion("RPFSERIALNO is null");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoIsNotNull() {
            addCriterion("RPFSERIALNO is not null");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoEqualTo(String value) {
            addCriterion("RPFSERIALNO =", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoNotEqualTo(String value) {
            addCriterion("RPFSERIALNO <>", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoGreaterThan(String value) {
            addCriterion("RPFSERIALNO >", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoGreaterThanOrEqualTo(String value) {
            addCriterion("RPFSERIALNO >=", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLessThan(String value) {
            addCriterion("RPFSERIALNO <", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLessThanOrEqualTo(String value) {
            addCriterion("RPFSERIALNO <=", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RPFSERIALNO <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLike(String value) {
            addCriterion("RPFSERIALNO like", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoNotLike(String value) {
            addCriterion("RPFSERIALNO not like", value, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoIn(List<String> values) {
            addCriterion("RPFSERIALNO in", values, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoNotIn(List<String> values) {
            addCriterion("RPFSERIALNO not in", values, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoBetween(String value1, String value2) {
            addCriterion("RPFSERIALNO between", value1, value2, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoNotBetween(String value1, String value2) {
            addCriterion("RPFSERIALNO not between", value1, value2, "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andCertidIsNull() {
            addCriterion("CERTID is null");
            return (Criteria) this;
        }

        public Criteria andCertidIsNotNull() {
            addCriterion("CERTID is not null");
            return (Criteria) this;
        }

        public Criteria andCertidEqualTo(String value) {
            addCriterion("CERTID =", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("CERTID = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidNotEqualTo(String value) {
            addCriterion("CERTID <>", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CERTID <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidGreaterThan(String value) {
            addCriterion("CERTID >", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("CERTID > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidGreaterThanOrEqualTo(String value) {
            addCriterion("CERTID >=", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CERTID >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidLessThan(String value) {
            addCriterion("CERTID <", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("CERTID < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidLessThanOrEqualTo(String value) {
            addCriterion("CERTID <=", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CERTID <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCertidLike(String value) {
            addCriterion("CERTID like", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidNotLike(String value) {
            addCriterion("CERTID not like", value, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidIn(List<String> values) {
            addCriterion("CERTID in", values, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidNotIn(List<String> values) {
            addCriterion("CERTID not in", values, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidBetween(String value1, String value2) {
            addCriterion("CERTID between", value1, value2, "certid");
            return (Criteria) this;
        }

        public Criteria andCertidNotBetween(String value1, String value2) {
            addCriterion("CERTID not between", value1, value2, "certid");
            return (Criteria) this;
        }

        public Criteria andCustomerNoIsNull() {
            addCriterion("CUSTOMER_NO is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNoIsNotNull() {
            addCriterion("CUSTOMER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNoEqualTo(String value) {
            addCriterion("CUSTOMER_NO =", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoNotEqualTo(String value) {
            addCriterion("CUSTOMER_NO <>", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoGreaterThan(String value) {
            addCriterion("CUSTOMER_NO >", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoGreaterThanOrEqualTo(String value) {
            addCriterion("CUSTOMER_NO >=", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoLessThan(String value) {
            addCriterion("CUSTOMER_NO <", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoLessThanOrEqualTo(String value) {
            addCriterion("CUSTOMER_NO <=", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMER_NO <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomerNoLike(String value) {
            addCriterion("CUSTOMER_NO like", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoNotLike(String value) {
            addCriterion("CUSTOMER_NO not like", value, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoIn(List<String> values) {
            addCriterion("CUSTOMER_NO in", values, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoNotIn(List<String> values) {
            addCriterion("CUSTOMER_NO not in", values, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoBetween(String value1, String value2) {
            addCriterion("CUSTOMER_NO between", value1, value2, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomerNoNotBetween(String value1, String value2) {
            addCriterion("CUSTOMER_NO not between", value1, value2, "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomernameIsNull() {
            addCriterion("CUSTOMERNAME is null");
            return (Criteria) this;
        }

        public Criteria andCustomernameIsNotNull() {
            addCriterion("CUSTOMERNAME is not null");
            return (Criteria) this;
        }

        public Criteria andCustomernameEqualTo(String value) {
            addCriterion("CUSTOMERNAME =", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameNotEqualTo(String value) {
            addCriterion("CUSTOMERNAME <>", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameGreaterThan(String value) {
            addCriterion("CUSTOMERNAME >", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameGreaterThanOrEqualTo(String value) {
            addCriterion("CUSTOMERNAME >=", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameLessThan(String value) {
            addCriterion("CUSTOMERNAME <", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameLessThanOrEqualTo(String value) {
            addCriterion("CUSTOMERNAME <=", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CUSTOMERNAME <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomernameLike(String value) {
            addCriterion("CUSTOMERNAME like", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameNotLike(String value) {
            addCriterion("CUSTOMERNAME not like", value, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameIn(List<String> values) {
            addCriterion("CUSTOMERNAME in", values, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameNotIn(List<String> values) {
            addCriterion("CUSTOMERNAME not in", values, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameBetween(String value1, String value2) {
            addCriterion("CUSTOMERNAME between", value1, value2, "customername");
            return (Criteria) this;
        }

        public Criteria andCustomernameNotBetween(String value1, String value2) {
            addCriterion("CUSTOMERNAME not between", value1, value2, "customername");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmIsNull() {
            addCriterion("MOBILE_PROV_NM is null");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmIsNotNull() {
            addCriterion("MOBILE_PROV_NM is not null");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmEqualTo(String value) {
            addCriterion("MOBILE_PROV_NM =", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmNotEqualTo(String value) {
            addCriterion("MOBILE_PROV_NM <>", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmGreaterThan(String value) {
            addCriterion("MOBILE_PROV_NM >", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmGreaterThanOrEqualTo(String value) {
            addCriterion("MOBILE_PROV_NM >=", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLessThan(String value) {
            addCriterion("MOBILE_PROV_NM <", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLessThanOrEqualTo(String value) {
            addCriterion("MOBILE_PROV_NM <=", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("MOBILE_PROV_NM <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLike(String value) {
            addCriterion("MOBILE_PROV_NM like", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmNotLike(String value) {
            addCriterion("MOBILE_PROV_NM not like", value, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmIn(List<String> values) {
            addCriterion("MOBILE_PROV_NM in", values, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmNotIn(List<String> values) {
            addCriterion("MOBILE_PROV_NM not in", values, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmBetween(String value1, String value2) {
            addCriterion("MOBILE_PROV_NM between", value1, value2, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmNotBetween(String value1, String value2) {
            addCriterion("MOBILE_PROV_NM not between", value1, value2, "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmIsNull() {
            addCriterion("ID_NO_PROV_NM is null");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmIsNotNull() {
            addCriterion("ID_NO_PROV_NM is not null");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmEqualTo(String value) {
            addCriterion("ID_NO_PROV_NM =", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmNotEqualTo(String value) {
            addCriterion("ID_NO_PROV_NM <>", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmGreaterThan(String value) {
            addCriterion("ID_NO_PROV_NM >", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmGreaterThanOrEqualTo(String value) {
            addCriterion("ID_NO_PROV_NM >=", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLessThan(String value) {
            addCriterion("ID_NO_PROV_NM <", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLessThanOrEqualTo(String value) {
            addCriterion("ID_NO_PROV_NM <=", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("ID_NO_PROV_NM <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLike(String value) {
            addCriterion("ID_NO_PROV_NM like", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmNotLike(String value) {
            addCriterion("ID_NO_PROV_NM not like", value, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmIn(List<String> values) {
            addCriterion("ID_NO_PROV_NM in", values, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmNotIn(List<String> values) {
            addCriterion("ID_NO_PROV_NM not in", values, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmBetween(String value1, String value2) {
            addCriterion("ID_NO_PROV_NM between", value1, value2, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmNotBetween(String value1, String value2) {
            addCriterion("ID_NO_PROV_NM not between", value1, value2, "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceIsNull() {
            addCriterion("LIVE_PROVINCE is null");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceIsNotNull() {
            addCriterion("LIVE_PROVINCE is not null");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceEqualTo(String value) {
            addCriterion("LIVE_PROVINCE =", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceNotEqualTo(String value) {
            addCriterion("LIVE_PROVINCE <>", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceGreaterThan(String value) {
            addCriterion("LIVE_PROVINCE >", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("LIVE_PROVINCE >=", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLessThan(String value) {
            addCriterion("LIVE_PROVINCE <", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLessThanOrEqualTo(String value) {
            addCriterion("LIVE_PROVINCE <=", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("LIVE_PROVINCE <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLike(String value) {
            addCriterion("LIVE_PROVINCE like", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceNotLike(String value) {
            addCriterion("LIVE_PROVINCE not like", value, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceIn(List<String> values) {
            addCriterion("LIVE_PROVINCE in", values, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceNotIn(List<String> values) {
            addCriterion("LIVE_PROVINCE not in", values, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceBetween(String value1, String value2) {
            addCriterion("LIVE_PROVINCE between", value1, value2, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceNotBetween(String value1, String value2) {
            addCriterion("LIVE_PROVINCE not between", value1, value2, "liveProvince");
            return (Criteria) this;
        }

        public Criteria andIpProvCnIsNull() {
            addCriterion("IP_PROV_CN is null");
            return (Criteria) this;
        }

        public Criteria andIpProvCnIsNotNull() {
            addCriterion("IP_PROV_CN is not null");
            return (Criteria) this;
        }

        public Criteria andIpProvCnEqualTo(String value) {
            addCriterion("IP_PROV_CN =", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnNotEqualTo(String value) {
            addCriterion("IP_PROV_CN <>", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnGreaterThan(String value) {
            addCriterion("IP_PROV_CN >", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnGreaterThanOrEqualTo(String value) {
            addCriterion("IP_PROV_CN >=", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnLessThan(String value) {
            addCriterion("IP_PROV_CN <", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnLessThanOrEqualTo(String value) {
            addCriterion("IP_PROV_CN <=", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("IP_PROV_CN <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIpProvCnLike(String value) {
            addCriterion("IP_PROV_CN like", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnNotLike(String value) {
            addCriterion("IP_PROV_CN not like", value, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnIn(List<String> values) {
            addCriterion("IP_PROV_CN in", values, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnNotIn(List<String> values) {
            addCriterion("IP_PROV_CN not in", values, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnBetween(String value1, String value2) {
            addCriterion("IP_PROV_CN between", value1, value2, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andIpProvCnNotBetween(String value1, String value2) {
            addCriterion("IP_PROV_CN not between", value1, value2, "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andSuccessIsNull() {
            addCriterion("SUCCESS is null");
            return (Criteria) this;
        }

        public Criteria andSuccessIsNotNull() {
            addCriterion("SUCCESS is not null");
            return (Criteria) this;
        }

        public Criteria andSuccessEqualTo(String value) {
            addCriterion("SUCCESS =", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessNotEqualTo(String value) {
            addCriterion("SUCCESS <>", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessGreaterThan(String value) {
            addCriterion("SUCCESS >", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessGreaterThanOrEqualTo(String value) {
            addCriterion("SUCCESS >=", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessLessThan(String value) {
            addCriterion("SUCCESS <", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessLessThanOrEqualTo(String value) {
            addCriterion("SUCCESS <=", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("SUCCESS <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSuccessLike(String value) {
            addCriterion("SUCCESS like", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessNotLike(String value) {
            addCriterion("SUCCESS not like", value, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessIn(List<String> values) {
            addCriterion("SUCCESS in", values, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessNotIn(List<String> values) {
            addCriterion("SUCCESS not in", values, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessBetween(String value1, String value2) {
            addCriterion("SUCCESS between", value1, value2, "success");
            return (Criteria) this;
        }

        public Criteria andSuccessNotBetween(String value1, String value2) {
            addCriterion("SUCCESS not between", value1, value2, "success");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("RESULT is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("RESULT is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(String value) {
            addCriterion("RESULT =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("RESULT = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(String value) {
            addCriterion("RESULT <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESULT <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(String value) {
            addCriterion("RESULT >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("RESULT > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(String value) {
            addCriterion("RESULT >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESULT >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultLessThan(String value) {
            addCriterion("RESULT <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(new StringBuilder("RESULT < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(String value) {
            addCriterion("RESULT <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESULT <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResultLike(String value) {
            addCriterion("RESULT like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotLike(String value) {
            addCriterion("RESULT not like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<String> values) {
            addCriterion("RESULT in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<String> values) {
            addCriterion("RESULT not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(String value1, String value2) {
            addCriterion("RESULT between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(String value1, String value2) {
            addCriterion("RESULT not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNull() {
            addCriterion("CREATETIME is null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNotNull() {
            addCriterion("CREATETIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeEqualTo(Date value) {
            addCriterion("CREATETIME =", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotEqualTo(Date value) {
            addCriterion("CREATETIME <>", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThan(Date value) {
            addCriterion("CREATETIME >", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATETIME >=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThan(Date value) {
            addCriterion("CREATETIME <", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATETIME <=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("CREATETIME <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeIn(List<Date> values) {
            addCriterion("CREATETIME in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotIn(List<Date> values) {
            addCriterion("CREATETIME not in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeBetween(Date value1, Date value2) {
            addCriterion("CREATETIME between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATETIME not between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeIsNull() {
            addCriterion("RESPONSETIME is null");
            return (Criteria) this;
        }

        public Criteria andResponsetimeIsNotNull() {
            addCriterion("RESPONSETIME is not null");
            return (Criteria) this;
        }

        public Criteria andResponsetimeEqualTo(Double value) {
            addCriterion("RESPONSETIME =", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeNotEqualTo(Double value) {
            addCriterion("RESPONSETIME <>", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeNotEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeGreaterThan(Double value) {
            addCriterion("RESPONSETIME >", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeGreaterThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeGreaterThanOrEqualTo(Double value) {
            addCriterion("RESPONSETIME >=", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeGreaterThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeLessThan(Double value) {
            addCriterion("RESPONSETIME <", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeLessThanColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeLessThanOrEqualTo(Double value) {
            addCriterion("RESPONSETIME <=", value, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeLessThanOrEqualToColumn(RiskImageLogDO.Column column) {
            addCriterion(
                new StringBuilder("RESPONSETIME <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andResponsetimeIn(List<Double> values) {
            addCriterion("RESPONSETIME in", values, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeNotIn(List<Double> values) {
            addCriterion("RESPONSETIME not in", values, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeBetween(Double value1, Double value2) {
            addCriterion("RESPONSETIME between", value1, value2, "responsetime");
            return (Criteria) this;
        }

        public Criteria andResponsetimeNotBetween(Double value1, Double value2) {
            addCriterion("RESPONSETIME not between", value1, value2, "responsetime");
            return (Criteria) this;
        }

        public Criteria andSerialnoLikeInsensitive(String value) {
            addCriterion("upper(SERIALNO) like", value.toUpperCase(), "serialno");
            return (Criteria) this;
        }

        public Criteria andRpfserialnoLikeInsensitive(String value) {
            addCriterion("upper(RPFSERIALNO) like", value.toUpperCase(), "rpfserialno");
            return (Criteria) this;
        }

        public Criteria andCertidLikeInsensitive(String value) {
            addCriterion("upper(CERTID) like", value.toUpperCase(), "certid");
            return (Criteria) this;
        }

        public Criteria andCustomerNoLikeInsensitive(String value) {
            addCriterion("upper(CUSTOMER_NO) like", value.toUpperCase(), "customerNo");
            return (Criteria) this;
        }

        public Criteria andCustomernameLikeInsensitive(String value) {
            addCriterion("upper(CUSTOMERNAME) like", value.toUpperCase(), "customername");
            return (Criteria) this;
        }

        public Criteria andMobileProvNmLikeInsensitive(String value) {
            addCriterion("upper(MOBILE_PROV_NM) like", value.toUpperCase(), "mobileProvNm");
            return (Criteria) this;
        }

        public Criteria andIdNoProvNmLikeInsensitive(String value) {
            addCriterion("upper(ID_NO_PROV_NM) like", value.toUpperCase(), "idNoProvNm");
            return (Criteria) this;
        }

        public Criteria andLiveProvinceLikeInsensitive(String value) {
            addCriterion("upper(LIVE_PROVINCE) like", value.toUpperCase(), "liveProvince");
            return (Criteria) this;
        }

        public Criteria andIpProvCnLikeInsensitive(String value) {
            addCriterion("upper(IP_PROV_CN) like", value.toUpperCase(), "ipProvCn");
            return (Criteria) this;
        }

        public Criteria andSuccessLikeInsensitive(String value) {
            addCriterion("upper(SUCCESS) like", value.toUpperCase(), "success");
            return (Criteria) this;
        }

        public Criteria andResultLikeInsensitive(String value) {
            addCriterion("upper(RESULT) like", value.toUpperCase(), "result");
            return (Criteria) this;
        }
    }

    /**
     * <AUTHOR>
     * @Date 2021-8-8 10:30:05
     */
    public static class Criteria extends GeneratedCriteria {

        private final RiskImageLogQuery example;

        protected Criteria(RiskImageLogQuery example) {
            super();
            this.example = example;
        }

        public RiskImageLogQuery example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        /**
         * <AUTHOR>
         * @Date 2021-8-8 10:30:05
         */
        @Deprecated
        public interface ICriteriaAdd {

            Criteria add(Criteria add);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2021-8-8 10:30:05
     */
    public static class Criterion {

        private final String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private final String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2021-8-8 10:30:05
     */
    public interface ICriteriaWhen {

        void criteria(Criteria criteria);
    }

    /**
     * <AUTHOR>
     * @Date 2021-8-8 10:30:05
     */
    public interface IExampleWhen {

        void example(RiskImageLogQuery example);
    }
}