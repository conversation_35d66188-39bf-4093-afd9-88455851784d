package com.bestpay.media.platform.rc.gateway.background.controller.impl;

import com.bestpay.media.platform.rc.gateway.background.component.vector.SceneVector;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.controller.SceneManageController;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.AddSceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.DeleteSceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.QuerySceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.QuerySceneResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.background.service.ProductCacheService;
import com.bestpay.media.platform.rc.gateway.background.service.RiskImageService;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.util.ResponseUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Author: 段然
 * @Date: 2024/9/11
 */
@Slf4j
@Filter
@Validated
@RestController
public class SceneManagerControllerImpl implements SceneManageController {

    private final RiskImageConfig config;
    private final CollectionUtil collectionUtil;
    private final RiskImageService riskImageService;
    private final ProductCacheService productCacheService;
    private final VectorEngineServiceProvider serviceProvider;

    public SceneManagerControllerImpl(CollectionUtil collectionUtil, RiskImageConfig config, RiskImageService riskImageService,
        ProductCacheService productCacheService, VectorEngineServiceProvider serviceProvider) {
        this.config = config;
        this.collectionUtil = collectionUtil;
        this.serviceProvider = serviceProvider;
        this.riskImageService = riskImageService;
        this.productCacheService = productCacheService;
    }

    @Override
    public Response<String> addScene(AddSceneRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        ExtractResult extracted = riskImageService.extract(request.getBase64(), request.getName());

        String collectionName = collectionUtil.buildSceneCollectionName(productId);
        VectorService<SceneVector> vectorService = serviceProvider.getVectorService(collectionName, SceneVector.class);

        SceneVector vector = SceneVector.builder()
            .id(request.getName())
            .name(request.getName())
            .threshold(ObjectUtils.firstNonNull(request.getThreshold(), config.getDefaultSceneThreshold()))
            .vector(extracted.getImg_feature())
            .noise(extracted.getNoise())
            .creation_date(System.currentTimeMillis())
            .product_id(productId)
            .build();

        String insertedId = vectorService.insertVector(vector);

        productCache.addScene();

        return ResponseUtil.success(insertedId);
    }

    @Override
    public Response<Boolean> deleteScene(DeleteSceneRequestDTO request) {
        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildSceneCollectionName(productId);
        VectorService<SceneVector> vectorService = serviceProvider.getVectorService(collectionName, SceneVector.class);

        boolean deleted = vectorService.deleteVectorById(request.getName());

        productCache.deleteScene();

        return ResponseUtil.success(deleted);
    }

    @Override
    public Response<List<QuerySceneResponseDTO>> queryScene(QuerySceneRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();

        String collectionName = collectionUtil.buildSceneCollectionName(productId);
        VectorService<SceneVector> vectorService = serviceProvider.getVectorService(collectionName, SceneVector.class);

        // TODO 添加查询分页
        List<SceneVector> vectors = vectorService.queryVectorList(0,  config.getMaxSearchSize());

        List<QuerySceneResponseDTO> response = vectors.isEmpty() ? Collections.emptyList() :
            vectors.stream()
                .map(result ->
                    QuerySceneResponseDTO.builder()
                        .name(result.getName())
                        .threshold(result.getThreshold())
                        .productId(result.getProduct_id())
                        .build())
                .collect(Collectors.toList());

        return ResponseUtil.success(response);
    }
}
