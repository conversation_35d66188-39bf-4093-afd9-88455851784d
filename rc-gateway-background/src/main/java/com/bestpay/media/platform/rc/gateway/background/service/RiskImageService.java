package com.bestpay.media.platform.rc.gateway.background.service;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.BAD_GATEWAY;

import com.alibaba.fastjson2.JSON;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.model.RiskImageModelService;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateRequestDTO.CalculateData;
import com.bestpay.media.platform.rc.gateway.background.model.entity.CalculateResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractRequestDTO.ExtractRequestData;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.util.VectorUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/9/12
 */
@Slf4j
@Component
public class RiskImageService {

    private final RiskImageConfig config;
    private final RiskImageModelService riskImageModelService;

    public RiskImageService(RiskImageConfig config, RiskImageModelService riskImageModelService) {
        this.config = config;
        this.riskImageModelService = riskImageModelService;
    }

    public ExtractResult extract(String base64, String customerNo) {
        ExtractRequestDTO request = ExtractRequestDTO.builder()
            .data(ExtractRequestData.builder()
                .base64(base64)
                .customer_no(customerNo)
                .build())
            .build();
        ExtractResponseDTO response;
        try {
            response = riskImageModelService.extract(request);
        } catch (Exception e) {
            log.error("背景照模型提取失败", e);
            throw new GateWayException(BAD_GATEWAY);
        }
        return checkResponse(response);
    }


    public String calculate(CalculateData calculateData) {
        CalculateRequestDTO request = CalculateRequestDTO.builder()
            .data(calculateData)
            .build();
        CalculateResponseDTO response;
        try {
            response = riskImageModelService.calculate(request);
        } catch (Exception e) {
            throw new GateWayException(BAD_GATEWAY, e);
        }
        return checkResponse(response);
    }

    private ExtractResult checkResponse(ExtractResponseDTO extractResponse) {
        if (extractResponse != null &&
            extractResponse.getSuccess() != null &&
            extractResponse.getResult() != null &&
            Boolean.parseBoolean(extractResponse.getSuccess())) {
            ExtractResult result;
            try {
                result = JSON.parseObject(extractResponse.getResult(), ExtractResult.class);
            } catch (Exception e) {
                throw new GateWayException(BAD_GATEWAY, e);
            }
            if (result != null &&
                result.getNoise() != null &&
                result.getImg_feature() != null &&
                !result.getImg_feature().isEmpty() &&
                result.getImg_feature().size() == config.getVectorsDim()) {
                result.setImg_feature(normalization(result.getImg_feature()));
                return result;
            }
        }
        log.error("背景照模型提取失败, response:{}", extractResponse);
        throw new GateWayException(BAD_GATEWAY);
    }

    private String checkResponse(CalculateResponseDTO calculateResponse) {
        if (calculateResponse == null ||
            calculateResponse.getSuccess() == null || !
            Boolean.parseBoolean(calculateResponse.getSuccess()) ||
            calculateResponse.getResult() == null) {
            log.error("背景照模型计算失败, response:{}", calculateResponse);
            throw new GateWayException(BAD_GATEWAY);
        }
        return calculateResponse.getResult();
    }

    private List<Float> normalization(List<Float> vector) {
        return config.getEnableVectorNormalization()
            ? VectorUtil.normalization(vector)
            : vector;
    }

}
