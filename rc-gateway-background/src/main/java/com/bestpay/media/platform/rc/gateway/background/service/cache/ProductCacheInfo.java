package com.bestpay.media.platform.rc.gateway.background.service.cache;

import lombok.Builder;
import lombok.Data;
import org.redisson.api.RMap;
import org.redisson.api.RSet;

/**
 * @Author: 段然
 * @Date: 2024/9/12
 */
@Data
@Builder
public class ProductCacheInfo {

    private final RSet<String> requestIdSet;
    private final RMap<String, Object> metaMap;
    private final RMap<String, Long> vectorCounter;
    private final RMap<String, Long> idProvinceCounter;
    private final RMap<String, Long> ipProvinceCounter;
    private final RMap<String, Long> liveProvinceCounter;
    private final RMap<String, Long> mobileProvinceCounter;

}
