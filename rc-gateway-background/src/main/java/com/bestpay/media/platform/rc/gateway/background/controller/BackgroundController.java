package com.bestpay.media.platform.rc.gateway.background.controller;


import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CONTENT_TYPE_JSON;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import java.util.List;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> 2021/1/11
 */
@Validated
@RequestMapping(produces = CONTENT_TYPE_JSON, consumes = CONTENT_TYPE_JSON)
public interface BackgroundController {

    @Validated
    @RequestMapping(value = "/riskImage", method = POST)
    Response<String> riskImage(@RequestBody @Valid RiskImageRequestDTO request);

    @Validated
    @RequestMapping(value = "/analysis", method = POST)
    Response<AnalysisResponseDTO> analysis(@RequestBody @Valid AnalysisRequestDTO request);

    @Validated
    @RequestMapping(value = "/searchImage", method = POST)
    Response<List<SearchBackgroundResponseDTO>> searchImage(@RequestBody @Valid SearchBackgroundRequestDTO request);

}
