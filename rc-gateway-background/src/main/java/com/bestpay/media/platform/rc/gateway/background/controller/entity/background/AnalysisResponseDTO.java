package com.bestpay.media.platform.rc.gateway.background.controller.entity.background;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2021/1/13
 */

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnalysisResponseDTO {

    private Map<String, Long> idNoProvInfo;

    private Map<String, Long> ipProvCnInfo;

    private Map<String, Long> liveProvInfo;

    private Map<String, Long> mobileProvInfo;

}
