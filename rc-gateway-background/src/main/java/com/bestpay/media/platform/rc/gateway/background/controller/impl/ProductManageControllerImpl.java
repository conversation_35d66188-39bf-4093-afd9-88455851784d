package com.bestpay.media.platform.rc.gateway.background.controller.impl;

import static com.bestpay.media.platform.rc.gateway.common.component.VersionManager.GATEWAY_VERSION_KEY;

import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionParams;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionType;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.controller.ProductManageController;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.AddProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.DeleteProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.UpdateProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.service.ProductCacheService;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.component.VersionManager;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.util.ResponseUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.service.CollectionService;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.google.common.base.Strings;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Slf4j
@Filter
@Validated
@RestController
public class ProductManageControllerImpl implements ProductManageController {

    private final CollectionUtil collectionUtil;
    private final VersionManager versionManager;
    private final RiskImageConfig riskImageConfig;
    private final CollectionService collectionService;
    private final ProductCacheService productCacheService;

    @Autowired
    public ProductManageControllerImpl(CollectionUtil collectionUtil, VersionManager versionManager, RiskImageConfig riskImageConfig,
        VectorEngineServiceProvider serviceProvider, ProductCacheService productCacheService) {
        this.productCacheService = productCacheService;
        this.versionManager = versionManager;
        this.collectionUtil = collectionUtil;
        this.riskImageConfig = riskImageConfig;
        this.collectionService = serviceProvider.getCollectionService();
    }

    @Override
    public Response<Boolean> addProduct(@RequestBody @Valid AddProductRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductNotExist();

        String gatewayVersion = versionManager.getVersionJson().getJSONObject(GATEWAY_VERSION_KEY).toJSONString();
        long maxCollectionSize = ObjectUtils.firstNonNull(request.getMaxCollectionSize(), riskImageConfig.getDefaultMaxVectorsNum());

        CollectionMeta meta = CollectionMeta.builder()
            .productId(productId)
            .gatewayVersion(gatewayVersion)
            .maxCollectionSize(maxCollectionSize)
            .description(request.getDescription())
            .build();

        CollectionParams backgroundCollectionParams = collectionUtil.buildBackgroundCollectionParams(meta);
        CollectionParams sceneCollectionParams = collectionUtil.buildSceneCollectionParams(meta);
        CollectionParams blackCollectionParams = collectionUtil.buildBlackCollectionParams(meta);

        boolean backgroundCollectionCreated = false;
        boolean sceneCollectionCreated = false;
        boolean blackCollectionCreated = false;

        try {
            backgroundCollectionCreated = collectionService.createCollection(backgroundCollectionParams.getCollectionName(),
                backgroundCollectionParams);
            sceneCollectionCreated = collectionService.createCollection(sceneCollectionParams.getCollectionName(),
                sceneCollectionParams);
            blackCollectionCreated = collectionService.createCollection(blackCollectionParams.getCollectionName(),
                blackCollectionParams);
        } catch (Exception e) {
            if (backgroundCollectionCreated) {
                deleteQuietly(backgroundCollectionParams.getCollectionName());
            }
            if (sceneCollectionCreated) {
                deleteQuietly(sceneCollectionParams.getCollectionName());
            }
            throw e;
        }

        boolean created = backgroundCollectionCreated && sceneCollectionCreated && blackCollectionCreated;

        productCache.rebuildProductCache(true);

        return ResponseUtil.success(created);
    }

    private void deleteQuietly(String collectionName) {
        try {
            log.info("回滚删除Collection: {}", collectionName);
            collectionService.deleteCollection(collectionName);
        } catch (Exception e) {
            log.error("回滚删除Collection失败", e);
        }
    }

    @Override
    public Response<Boolean> deleteProduct(DeleteProductRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.rebuildProductCache(true);
        productCache.checkIfProductExist();

        String backgroundCollectionName = collectionUtil.buildBackgroundCollectionName(productId);
        String sceneCollectionName = collectionUtil.buildSceneCollectionName(productId);
        String blackCollectionName = collectionUtil.buildBlackCollectionName(productId);

        boolean backgroundCollectionDeleted = collectionService.deleteCollection(backgroundCollectionName);
        boolean sceneCollectionDeleted = collectionService.deleteCollection(sceneCollectionName);
        boolean blackCollectionDeleted = collectionService.deleteCollection(blackCollectionName);

        boolean deleted = backgroundCollectionDeleted && sceneCollectionDeleted && blackCollectionDeleted;

        productCache.clear();

        return ResponseUtil.success(deleted);

    }

    @Override
    public Response<Boolean> updateProduct(UpdateProductRequestDTO request) {

        String productId = request.getProductId();
        ProductCache productCache = productCacheService.getCache(productId);
        productCache.checkIfProductExist();
        // TODO 实现更新功能
        log.error("暂不支持更新产品信息");
        return ResponseUtil.success(false, "暂不支持更新产品信息");
    }

    @Override
    public Response<List<QueryProductResponseDTO>> queryProduct(QueryProductRequestDTO request) {

        //TODO 添加分页查询功能

        List<Collection> collectionList = Strings.isNullOrEmpty(request.getProductId()) ?
            collectionService.queryCollectionInfoList() :
            Collections.singletonList(
                collectionService.queryCollectionInfo(
                    collectionUtil.buildBackgroundCollectionName(request.getProductId())));

        if (collectionList.isEmpty()) {
            return ResponseUtil.success(Collections.emptyList());
        }

        List<QueryProductResponseDTO> productResponseList = collectionList
            .stream()
            .map(collection -> {
                CollectionMeta collectionMeta = JSONObject.parseObject(collection.getMeta(), CollectionMeta.class);
                if (collectionMeta == null ||
                    Strings.isNullOrEmpty(collectionMeta.getProductId()) ||
                    !CollectionType.BACKGROUND.name().equals(collectionMeta.getCollectionType())) {
                    return null;
                }
                return QueryProductResponseDTO.builder()
                    .productId(collectionMeta.getProductId())
                    .description(collectionMeta.getDescription())
                    .currentCollectionSize(collection.getCount())
                    .maxCollectionSize(collectionMeta.getMaxCollectionSize())
                    .build();
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        return ResponseUtil.success(productResponseList);
    }

}
