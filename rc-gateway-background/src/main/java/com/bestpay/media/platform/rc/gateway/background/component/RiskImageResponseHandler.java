package com.bestpay.media.platform.rc.gateway.background.component;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.controller.BackgroundController;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO;
import com.bestpay.media.platform.rc.gateway.common.config.DatabaseConfig;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.global.GlobalContext;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Executor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * @Author: 段然
 * @Date: 2024/4/7
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnBean(DatabaseConfig.class)
public class RiskImageResponseHandler implements ResponseBodyAdvice<Object>, RequestBodyAdvice {

    private final Method target;
    private final Executor executor;
    private final RedisManager redisManager;
    private final RiskImageConfig riskImageConfig;
    private final TransmittableThreadLocal<RiskImageRequestDTO> threadLocal;

    @Autowired
    @SneakyThrows
    public RiskImageResponseHandler(Executor executor, RedisManager redisManager,
        GlobalContext globalContext, RiskImageConfig riskImageConfig) {
        this.redisManager = redisManager;
        this.riskImageConfig = riskImageConfig;
        this.target = Objects.requireNonNull(
            BackgroundController.class.getMethod("riskImage", RiskImageRequestDTO.class));
        this.executor = executor;
        this.threadLocal = new TransmittableThreadLocal<>();
    }

    @Override
    public boolean supports(MethodParameter returnType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        // todo use annotation
        return target.equals(returnType.getMethod());
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
        MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType,
        ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof Response) {
            Response<String> res = (Response<String>) body; // TODO beans
            Date createTime = new Date();
            executor.execute(() -> {
                try {
                    redisManager.getQueue(riskImageConfig.getDbLogQueueName())
                        .offer(RiskImageLogDO.build(threadLocal.get(), res, createTime));
                } catch (Exception e) {
                    log.error("数据库队列推送失败", e);
                } finally {
                    threadLocal.remove();
                }
            });
        }
        return body;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        return target.equals(methodParameter.getMethod());
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter,
        Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        return inputMessage;
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter,
        Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        if (body instanceof RiskImageRequestDTO) {
            threadLocal.set((RiskImageRequestDTO) body);
        }
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage,
        MethodParameter parameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }
}
