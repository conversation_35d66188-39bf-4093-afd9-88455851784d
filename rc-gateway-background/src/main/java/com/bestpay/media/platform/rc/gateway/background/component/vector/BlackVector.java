package com.bestpay.media.platform.rc.gateway.background.component.vector;

import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BlackVector extends Vector {

    private Double threshold;

    private String noise;

    private String live_province;

    private String product_id;

}
