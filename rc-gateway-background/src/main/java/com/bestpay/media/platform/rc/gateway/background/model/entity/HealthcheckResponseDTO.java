package com.bestpay.media.platform.rc.gateway.background.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 2021/1/13
 */
@Data
@Slf4j
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HealthcheckResponseDTO implements Serializable {

    private String success;

    private Integer code;

    private String result;

    private String help;

    private String time;

}
