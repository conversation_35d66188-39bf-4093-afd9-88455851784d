package com.bestpay.media.platform.rc.gateway.background.dal.mapper;

import com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO;
import com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogQuery;
import com.bestpay.media.platform.rc.gateway.common.config.DatabaseConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

@Mapper
@ConditionalOnBean(DatabaseConfig.class)
public interface RiskImageLogMapper {

    long countByExample(RiskImageLogQuery example);

    int deleteByExample(RiskImageLogQuery example);

    int deleteByPrimaryKey(String serialno);

    int insert(RiskImageLogDO record);

    int batchInsert(@Param("scheme") String scheme, @Param("tableName") String tableName,
        @Param("list") List<RiskImageLogDO> list);

    int insertSelective(@Param("record") RiskImageLogDO record,
        @Param("selective") RiskImageLogDO.Column... selective);

    RiskImageLogDO selectOneByExample(RiskImageLogQuery example);

    RiskImageLogDO selectOneByExampleSelective(@Param("example") RiskImageLogQuery example,
        @Param("selective") RiskImageLogDO.Column... selective);

    List<RiskImageLogDO> selectByExampleSelective(@Param("example") RiskImageLogQuery example,
        @Param("selective") RiskImageLogDO.Column... selective);

    List<RiskImageLogDO> selectByExample(RiskImageLogQuery example);

    RiskImageLogDO selectByPrimaryKeySelective(@Param("serialno") String serialno,
        @Param("selective") RiskImageLogDO.Column... selective);

    RiskImageLogDO selectByPrimaryKey(String serialno);

    int updateByExampleSelective(@Param("record") RiskImageLogDO record,
        @Param("example") RiskImageLogQuery example,
        @Param("selective") RiskImageLogDO.Column... selective);

    int updateByExample(@Param("record") RiskImageLogDO record,
        @Param("example") RiskImageLogQuery example);

    int updateByPrimaryKeySelective(@Param("record") RiskImageLogDO record,
        @Param("selective") RiskImageLogDO.Column... selective);

    int updateByPrimaryKey(RiskImageLogDO record);
}