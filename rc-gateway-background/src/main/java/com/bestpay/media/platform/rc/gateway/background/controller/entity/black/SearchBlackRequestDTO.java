package com.bestpay.media.platform.rc.gateway.background.controller.entity.black;

import com.alibaba.fastjson2.annotation.JSONField;
import com.bestpay.media.platform.rc.gateway.common.entity.Request;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.ToString.Exclude;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2021/1/13
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchBlackRequestDTO extends Request {

    @NotBlank
    @Size(min = 4, max = 64)
    @Pattern(regexp = "^[a-z0-9_]+$", message = "productId只能由小写字母、数字、下划线组成")
    private String productId;

    @Exclude
    @NotBlank
    @JSONField(serialize = false)
    @Pattern(regexp = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$", message = "%s不是有效的base64编码")
    private String base64;

    @NotNull
    @Max(100)
    private Integer topN;

    @NotNull
    @Max(1)
    private Float threshold;

    @NotNull
    @NotBlank
    private String liveProvince;
}
