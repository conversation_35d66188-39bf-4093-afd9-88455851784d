package com.bestpay.media.platform.rc.gateway.background.component;

import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/9/11
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class CollectionParams extends CollectionParam {

    private String collectionName;

}
