package com.bestpay.media.platform.rc.gateway.background.service;

import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/7
 */
@Component
public class ProductCacheService {

    private final RedisManager redisManager;
    private final CollectionUtil collectionUtil;
    private final RiskImageConfig riskImageConfig;
    private final VectorEngineServiceProvider serviceProvider;

    private final Map<String, ProductCache> productCacheMap;

    @Autowired
    public ProductCacheService(RedisManager redisManager, VectorEngineServiceProvider serviceProvider,
        RiskImageConfig riskImageConfig, CollectionUtil collectionUtil) {
        this.redisManager = redisManager;
        this.serviceProvider = serviceProvider;
        this.riskImageConfig = riskImageConfig;
        this.collectionUtil = collectionUtil;
        this.productCacheMap = new ConcurrentHashMap<>();
    }

    public ProductCache getCache(String productId) {
        ProductCache productCache = productCacheMap.get(productId);
        if (productCache != null) {
            return productCache;
        }
        return productCacheMap.computeIfAbsent(productId, this::createCache);
    }

    private ProductCache createCache(String productId) {
        return new ProductCache(serviceProvider, collectionUtil, redisManager,
            riskImageConfig.getCacheRebuildDelay(), riskImageConfig.getCacheLockLeaseTime(), productId);
    }
}
