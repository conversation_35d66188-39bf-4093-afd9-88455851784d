package com.bestpay.media.platform.rc.gateway.background.controller;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CONTENT_TYPE_JSON;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.AddSceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.DeleteSceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.QuerySceneRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.scene.QuerySceneResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import java.util.List;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> 2021/1/11
 */
@Validated
@RequestMapping(produces = CONTENT_TYPE_JSON, consumes = CONTENT_TYPE_JSON)
public interface SceneManageController {

    @RequestMapping(value = "/addScene", method = POST)
    Response<String> addScene(@RequestBody @Valid AddSceneRequestDTO request);

    @RequestMapping(value = "/deleteScene", method = POST)
    Response<Boolean> deleteScene(@RequestBody @Valid DeleteSceneRequestDTO request);

    @RequestMapping(value = "/queryScene", method = POST)
    Response<List<QuerySceneResponseDTO>> queryScene(@RequestBody @Valid QuerySceneRequestDTO request);

}
