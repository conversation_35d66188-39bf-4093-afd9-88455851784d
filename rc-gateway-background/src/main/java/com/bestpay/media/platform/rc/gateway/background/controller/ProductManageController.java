package com.bestpay.media.platform.rc.gateway.background.controller;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CONTENT_TYPE_JSON;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.AddProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.DeleteProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.UpdateProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import java.util.List;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> 2021/1/11
 */
@Validated
@RequestMapping(produces = CONTENT_TYPE_JSON, consumes = CONTENT_TYPE_JSON)
public interface ProductManageController {

    @RequestMapping(value = "/addProduct", method = POST)
    Response<Boolean> addProduct(@RequestBody @Valid AddProductRequestDTO request);

    @RequestMapping(value = "/deleteProduct", method = POST)
    Response<Boolean> deleteProduct(@RequestBody @Valid DeleteProductRequestDTO request);

    @RequestMapping(value = "/updateProduct", method = POST)
    Response<Boolean> updateProduct(@RequestBody @Valid UpdateProductRequestDTO request);

    @RequestMapping(value = "/queryProduct", method = POST)
    Response<List<QueryProductResponseDTO>> queryProduct(@RequestBody @Valid QueryProductRequestDTO request);

}
