package com.bestpay.media.platform.rc.gateway.background.util;

import static com.bestpay.media.platform.rc.gateway.background.component.CollectionType.BACKGROUND;
import static com.bestpay.media.platform.rc.gateway.background.component.CollectionType.BLACK;
import static com.bestpay.media.platform.rc.gateway.background.component.CollectionType.SCENE;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_BACKGROUND_IMAGE_MAPPING_PATH;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_BACKGROUND_IMAGE_SETTING_PATH;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_BLACK_IMAGE_MAPPING_PATH;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_BLACK_IMAGE_SETTING_PATH;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_MAX_SEARCH_WINDOW_KEY;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_PLACEHOLDER_PREFIX;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_PLACEHOLDER_SEPARATOR;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_PLACEHOLDER_SUFFIX;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_SCENE_IMAGE_MAPPING_PATH;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.ES_SCENE_IMAGE_SETTING_PATH;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_COLLECTION_NAME_EMPTY;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionParams;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionType;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.util.ResourceFileUtil;
import com.google.common.base.Strings;
import java.util.function.Function;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;

/**
 * @Author: 段然
 * @Date: 2024/4/7
 */
@Component
public class CollectionUtil {

    private final PropertyPlaceholderHelper placeholderHelper;
    private final RiskImageConfig config;
    private final JSONObject configJson;

    @Autowired
    public CollectionUtil(RiskImageConfig config) {
        this.config = config;
        this.configJson = JSONObject.from(config);
        this.placeholderHelper =
            new PropertyPlaceholderHelper(ES_PLACEHOLDER_PREFIX, ES_PLACEHOLDER_SUFFIX, ES_PLACEHOLDER_SEPARATOR, false);
    }

    public CollectionParams buildBackgroundCollectionParams(CollectionMeta meta) {
        return buildCollectionParams(meta, BACKGROUND,
            this::buildBackgroundCollectionName,
            ES_BACKGROUND_IMAGE_MAPPING_PATH,
            ES_BACKGROUND_IMAGE_SETTING_PATH);
    }

    public CollectionParams buildSceneCollectionParams(CollectionMeta meta) {
        return buildCollectionParams(meta, SCENE,
            this::buildSceneCollectionName,
            ES_SCENE_IMAGE_MAPPING_PATH,
            ES_SCENE_IMAGE_SETTING_PATH);
    }

    public CollectionParams buildBlackCollectionParams(CollectionMeta meta) {
        return buildCollectionParams(meta, BLACK,
            this::buildBlackCollectionName,
            ES_BLACK_IMAGE_MAPPING_PATH,
            ES_BLACK_IMAGE_SETTING_PATH);
    }

    private CollectionParams buildCollectionParams(CollectionMeta meta, CollectionType collectionType,
        Function<String, String> collectionNameBuilder, String mappingPath, String settingPath) {
        String collectionName = collectionNameBuilder.apply(meta.getProductId());
        JSONObject properties = JSONObject.from(meta);
        properties.putAll(configJson);
        properties.put(ES_MAX_SEARCH_WINDOW_KEY,
            ObjectUtils.firstNonNull(meta.getMaxCollectionSize(), config.getDefaultMaxVectorsNum()) + config.getMaxSearchSize());

        String setting = placeholderHelper.replacePlaceholders(ResourceFileUtil.readString(settingPath), properties::getString);
        String mapping = placeholderHelper.replacePlaceholders(ResourceFileUtil.readString(mappingPath), properties::getString);

        CollectionMeta newMeta = JSON.to(CollectionMeta.class, JSONObject.from(meta));
        newMeta.setCollectionType(collectionType.name());
        return CollectionParams.builder()
            .meta(JSON.toJSONString(newMeta))
            .collectionName(collectionName)
            .mapping(mapping)
            .setting(setting)
            .build();
    }

    public String buildSceneCollectionName(String collectionName) {
        if (Strings.isNullOrEmpty(collectionName)) {
            throw new GateWayException(INVALID_PARAM_COLLECTION_NAME_EMPTY);
        }
        return collectionName + config.getSceneCollectionSuffix();
    }

    public String buildBackgroundCollectionName(String collectionName) {
        if (Strings.isNullOrEmpty(collectionName)) {
            throw new GateWayException(INVALID_PARAM_COLLECTION_NAME_EMPTY);
        }
        return collectionName + config.getBackgroundCollectionSuffix();
    }

    public String buildBlackCollectionName(String collectionName) {
        if (Strings.isNullOrEmpty(collectionName)) {
            throw new GateWayException(INVALID_PARAM_COLLECTION_NAME_EMPTY);
        }
        return collectionName + config.getBlackCollectionSuffix();
    }


}
