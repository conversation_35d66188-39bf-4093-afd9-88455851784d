package com.bestpay.media.platform.rc.gateway.background.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "gateway.risk-image")
public class RiskImageConfig {

    private Boolean enableDefaultCollection;
    private Boolean enableVectorNormalization;
    private String dbLogQueueName;
    private Integer vectorsDim;
    private Integer numCandidates;
    private Integer topK;
    private Integer minK;
    private Long defaultMaxVectorsNum;
    private Integer minVectorsNum;
    private Integer maxSearchSize;
    private String backgroundCollectionSuffix;
    private String sceneCollectionSuffix;
    private String blackCollectionSuffix;
    private Integer maxDbInsertBatchSize;
    private Integer defaultShardsNum;
    private Double defaultSceneThreshold;
    private Double defaultBlackThreshold;
    private String defaultCollectionName;
    private Integer cacheRebuildDelay;
    private Integer cacheLockLeaseTime;
}
