package com.bestpay.media.platform.rc.gateway.background.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2021/1/13
 */
@Data
@ToString
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtractRequestDTO implements Serializable {

    private ExtractRequestData data;

    @Data
    @ToString
    @SuperBuilder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExtractRequestData implements Serializable {

        private final Integer index = 0;

        private String base64;

        private String customer_no;

    }
}
