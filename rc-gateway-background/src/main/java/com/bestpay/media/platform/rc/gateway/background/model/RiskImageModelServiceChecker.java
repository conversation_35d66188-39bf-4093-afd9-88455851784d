package com.bestpay.media.platform.rc.gateway.background.model;

import com.bestpay.media.platform.rc.gateway.background.model.entity.HealthcheckRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.HealthcheckRequestDTO.HealthcheckRequestData;
import com.bestpay.media.platform.rc.gateway.background.model.entity.HealthcheckResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Slf4j
@Component
public class RiskImageModelServiceChecker implements ModelServiceChecker<RiskImageModelService> {


    private final HealthcheckRequestDTO request;

    public RiskImageModelServiceChecker() {
        this.request = createRequest();
    }

    private HealthcheckRequestDTO createRequest() {
        return HealthcheckRequestDTO.builder()
            .data(HealthcheckRequestData.builder()
                .build())
            .build();
    }

    @Override
    public boolean check(RiskImageModelService modelService) {
        HealthcheckResponseDTO healthCheck = modelService.healthCheck(request);
        return healthCheck.getCode() == 200 && Boolean.parseBoolean(healthCheck.getSuccess());
    }

    @Override
    public String version(RiskImageModelService modelService) {
        HealthcheckResponseDTO healthCheck = modelService.healthCheck(request);
        return healthCheck.getResult();
    }


}
