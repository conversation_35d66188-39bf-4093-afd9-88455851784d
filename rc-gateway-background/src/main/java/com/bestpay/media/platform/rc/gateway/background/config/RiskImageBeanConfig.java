package com.bestpay.media.platform.rc.gateway.background.config;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.MODEL_SERVICE_NAME;

import com.bestpay.media.platform.rc.gateway.background.model.RiskImageModelService;
import com.bestpay.media.platform.rc.gateway.background.model.RiskImageModelServiceChecker;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 2021/1/15
 */
@Configuration
public class RiskImageBeanConfig {

    @Bean
    public RiskImageModelService riskImageModelService(ModelServiceManager modelServiceManager,
        RiskImageModelServiceChecker riskImageModelServiceChecker) {

        return modelServiceManager.registerModelService(MODEL_SERVICE_NAME,
            RiskImageModelService.class, riskImageModelServiceChecker);
    }

}
