package com.bestpay.media.platform.rc.gateway.background.task;

import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionType;
import com.bestpay.media.platform.rc.gateway.background.component.vector.BackgroundVector;
import com.bestpay.media.platform.rc.gateway.background.config.RiskImageConfig;
import com.bestpay.media.platform.rc.gateway.background.dal.mapper.RiskImageLogMapper;
import com.bestpay.media.platform.rc.gateway.background.dal.models.RiskImageLogDO;
import com.bestpay.media.platform.rc.gateway.background.service.ProductCacheService;
import com.bestpay.media.platform.rc.gateway.background.service.cache.ProductCache;
import com.bestpay.media.platform.rc.gateway.common.config.DatabaseConfig;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import com.google.common.base.Strings;
import java.util.List;
import java.util.concurrent.Executor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 *
 * <AUTHOR> 2019/10/23 0023
 */
@Slf4j
@Component
@EnableAsync
@Configuration
@EnableScheduling
@ConditionalOnMissingBean(type = "org.springframework.boot.test.mock.mockito.MockitoPostProcessor")
public class ScheduledTask {

    private final Executor executor;
    private final RedisManager redisManager;
    private final DatabaseConfig databaseConfig;
    private final RiskImageConfig riskImageConfig;
    private final RiskImageLogMapper riskImageLogMapper;
    private final ProductCacheService productCacheService;
    private final VectorEngineServiceProvider serviceProvider;

    public ScheduledTask(Executor executor, RedisManager redisManager,
        RiskImageConfig riskImageConfig, @Autowired(required = false) RiskImageLogMapper riskImageLogMapper,
        VectorEngineServiceProvider serviceProvider, @Autowired(required = false) DatabaseConfig databaseConfig,
        ProductCacheService productCacheService) {
        this.productCacheService = productCacheService;
        this.riskImageLogMapper = riskImageLogMapper;
        this.serviceProvider = serviceProvider;
        this.riskImageConfig = riskImageConfig;
        this.databaseConfig = databaseConfig;
        this.redisManager = redisManager;
        this.executor = executor;
    }

    /**
     * 定时进行数据落库，初始化延迟10秒，每10秒执行一次
     */
    @Scheduled(initialDelay = 10000, fixedDelay = 10000)
    public void pushResponseToDatabaseTask() {
        if (databaseConfig != null && Boolean.TRUE.equals(databaseConfig.getEnable())) {
            invoke(this::pushResponseToDatabase);
        }
    }

    private void pushResponseToDatabase() {
        String scheme = Strings.isNullOrEmpty(databaseConfig.getSchema()) ? "" : databaseConfig.getSchema();
        String tableName = databaseConfig.getTableName();
        RQueue<RiskImageLogDO> queue = redisManager.getQueue(riskImageConfig.getDbLogQueueName());
        List<RiskImageLogDO> poll = queue.poll(riskImageConfig.getMaxDbInsertBatchSize());
        if (poll != null && !poll.isEmpty()) {
            String customerNo = poll.stream().map(RiskImageLogDO::getCustomerNo).reduce((a, b) -> a + "," + b).get();
            try {
                log.info("开始执行数据库插入操作, 数据量:{}, 客户号:{}", poll.size(), customerNo);
                riskImageLogMapper.batchInsert(Strings.nullToEmpty(scheme), tableName, poll);
                log.info("成功执行数据库插入操作");
            } catch (Exception e) {
                log.info("数据库插入操作失败, 数据量:{}, 客户号:{}", poll.size(), customerNo, e);
            }

        }
    }

    /**
     * 定时进行数据清理，初始化延迟10分钟，每10分钟执行一次
     */
    @Scheduled(initialDelay = 600000, fixedDelay = 600000)
    public void removeVectorsTask() {
        invoke(this::removeVectors);
    }

    private void removeVectors() {
        List<Collection> collections = serviceProvider.getCollectionService().queryCollectionInfoList();
        if (collections.isEmpty()) {
            return;
        }
        for (Collection collection : collections) {
            CollectionMeta collectionMeta = JSONObject.parseObject(collection.getMeta(), CollectionMeta.class);
            if (collectionMeta == null ||
                Strings.isNullOrEmpty(collectionMeta.getProductId()) ||
                !CollectionType.BACKGROUND.name().equals(collectionMeta.getCollectionType())) {
                continue;
            }
            long maxCollectionSize = ObjectUtils.firstNonNull(collectionMeta.getMaxCollectionSize(),
                riskImageConfig.getDefaultMaxVectorsNum());
            if (collection.getCount() > maxCollectionSize) {
                removeVector(collection, collectionMeta);
            }
        }
    }


    @SneakyThrows
    private void removeVector(Collection collection, CollectionMeta collectionMeta) {
        executor.execute(() -> invoke(() -> {
            ProductCache productCache = productCacheService.getCache(collectionMeta.getProductId());
            String productName = productCache.getProductCacheInfo().getMetaMap().getName();
            VectorService<BackgroundVector> vectorService = serviceProvider.getVectorService(collection.getName(),
                BackgroundVector.class);
            executor.execute(() -> invoke(() -> productCache.invokeUnderLock(() ->
                vectorService.queryOldestVectors(Math.toIntExact(collectionMeta.getMaxCollectionSize()),
                    riskImageConfig.getMaxSearchSize()).forEach(vector -> {
                    try {
                        productCache.invokeUnderReadLock(() -> {
                            vectorService.deleteVectorById(vector.getId());
                            productCache.deleteBackground(vector);
                        });
                    } catch (Exception e) {
                        log.error("删除向量失败, id:{} .", vector.getId(), e);
                    }
                }), productName, 60000)));
        }));
    }

    private void invoke(Runnable task) {
        try {
            task.run();
        } catch (Throwable e) {
            log.error("定时任务执行失败", e);
        }
    }


}
