package com.bestpay.media.platform.rc.gateway.background.controller.entity.background;

import com.alibaba.fastjson2.annotation.JSONField;
import com.bestpay.media.platform.rc.gateway.common.entity.Request;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Strings;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.ToString.Exclude;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2021/1/13
 */


@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RiskImageRequestDTO extends Request {

    @Valid
    @NotNull
    private RiskImageData data;

    @Data
    @ToString
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RiskImageData {

        @NotBlank
        @Size(min = 4, max = 64)
        @Pattern(regexp = "^[a-z0-9_]+$", message = "productId只能由小写字母、数字、下划线组成")
        private String productId;

        @NotBlank
        private String rpfserialno;

        @NotBlank
        private String certid;

        @NotBlank
        private String customername;

        @NotBlank
        private String customer_no;

        private String mobile_prov_nm;

        private String id_no_prov_nm;

        private String live_province;

        private String ip_prov_cn;

        private String geo_info;

        @Exclude
        @JSONField(serialize = false)
        @Pattern(regexp = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$", message = "%s不是有效的base64编码")
        private String base64;

        private String insert_only;

        private String feature_only;

        @Exclude
        @JSONField(serialize = false)
        private List<Float> img_feature;

        private String noise;

        @JSONField(serialize = false)
        @AssertTrue(message = "base64不能为空")
        public boolean isBase64Valid() {
            boolean featureOnly = Boolean.parseBoolean(this.getFeature_only());
            return featureOnly || !Strings.isNullOrEmpty(this.getBase64());
        }

        @JSONField(serialize = false)
        @AssertTrue(message = "img_feature不能为空")
        public boolean isImgFeatureValid() {
            boolean featureOnly = Boolean.parseBoolean(this.getFeature_only());
            return !featureOnly || (this.getImg_feature() != null && !this.getImg_feature().isEmpty());
        }

        @JSONField(serialize = false)
        @AssertTrue(message = "noise不能为空")
        public boolean isNoiseValid() {
            boolean featureOnly = Boolean.parseBoolean(this.getFeature_only());
            return !featureOnly || !Strings.isNullOrEmpty(this.getNoise());
        }
    }

    @Override
    public String getRequestId() {
        if (getData() != null) {
            return getData().getRpfserialno();
        }
        return super.getRequestId();
    }

}
