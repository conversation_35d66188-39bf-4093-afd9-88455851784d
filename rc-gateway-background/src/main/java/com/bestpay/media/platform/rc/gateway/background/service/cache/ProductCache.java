package com.bestpay.media.platform.rc.gateway.background.service.cache;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_BACKGROUND_COUNT;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_BLACK_COUNT;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_ID_NO_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_ID_PROVINCE_COUNTER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_IP_NO_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_IP_PROVINCE_COUNTER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_LIVE_PROVINCE_COUNTER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_LIVE_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_LOCK;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_META_MAP;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_MOBILE_PROVINCE_COUNTER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_MOBILE_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_RWLOCK;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_SCENE_COUNT;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_VECTOR_COUNTER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_VECTOR_ID_SET;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_COLLECTION_ALREADY_EXISTS;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_COLLECTION_NOT_EXIST;

import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import com.bestpay.media.platform.rc.gateway.background.component.vector.BackgroundVector;
import com.bestpay.media.platform.rc.gateway.background.component.vector.BlackVector;
import com.bestpay.media.platform.rc.gateway.background.component.vector.SceneVector;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.service.CollectionService;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RFuture;
import org.redisson.api.RLock;
import org.redisson.api.RMapAsync;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RSetAsync;

/**
 * @Author: 段然
 * @Date: 2024/9/11
 */
@Slf4j
public class ProductCache {

    private final VectorEngineServiceProvider serviceProvider;
    private final CollectionUtil collectionUtil;
    private final RedisManager redisManager;
    private final int rebuildCacheDelay;
    private final int lockLeaseTime;

    private final String productId;


    private final RLock readLock;
    private final RLock writeLock;

    private final ProductCacheInfo productCacheInfo;

    public ProductCache(VectorEngineServiceProvider serviceProvider, CollectionUtil collectionUtil, RedisManager redisManager,
        int rebuildCacheDelay, int lockLeaseTime, String productId) {
        this.serviceProvider = serviceProvider;
        this.rebuildCacheDelay = rebuildCacheDelay;
        this.collectionUtil = collectionUtil;
        this.lockLeaseTime = lockLeaseTime;
        this.redisManager = redisManager;
        this.productId = productId;

        RReadWriteLock rwLock = redisManager.getRWLock(buildKey(CACHE_KEY_RWLOCK));

        this.readLock = rwLock.readLock();
        this.writeLock = rwLock.writeLock();
        this.productCacheInfo = buildCacheInfo();
    }

    private ProductCacheInfo buildCacheInfo() {
        return ProductCacheInfo.builder().metaMap(redisManager.getMap(buildKey(CACHE_KEY_META_MAP)))
            .requestIdSet(redisManager.getSet(buildKey(CACHE_KEY_VECTOR_ID_SET)))
            .vectorCounter(redisManager.getMap(buildKey(CACHE_KEY_VECTOR_COUNTER)))
            .idProvinceCounter(redisManager.getMap(buildKey(CACHE_KEY_ID_PROVINCE_COUNTER)))
            .ipProvinceCounter(redisManager.getMap(buildKey(CACHE_KEY_IP_PROVINCE_COUNTER)))
            .liveProvinceCounter(redisManager.getMap(buildKey(CACHE_KEY_LIVE_PROVINCE_COUNTER)))
            .mobileProvinceCounter(redisManager.getMap(buildKey(CACHE_KEY_MOBILE_PROVINCE_COUNTER))).build();
    }

    public boolean isProductExist() {
        if (isMetaCacheExist()) {
            return true;
        }
        return rebuildProductCache(false);
    }

    public void checkIfProductExist() {
        if (!isProductExist()) {
            throw new GateWayException(INVALID_PARAM_COLLECTION_NOT_EXIST);
        }
    }

    public void checkIfProductNotExist() {
        if (isProductExist()) {
            throw new GateWayException(INVALID_PARAM_COLLECTION_ALREADY_EXISTS);
        }
    }


    public ProductCacheInfo getProductCacheInfo() {
        if (!isProductExist()) {
            return null;
        }
        return productCacheInfo;
    }

    public void addScene() {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(
            () -> productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_SCENE_COUNT, (k, v) -> v == null ? 1 : Math.max(1, v + 1)));
    }

    public void deleteScene() {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(
            () -> productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_SCENE_COUNT, (k, v) -> v == null ? 0 : Math.max(0, v - 1)));
    }

    public void addBlack() {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(
            () -> productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_BLACK_COUNT, (k, v) -> v == null ? 1 : Math.max(1, v + 1)));
    }

    public void deleteBlack() {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(
            () -> productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_BLACK_COUNT, (k, v) -> v == null ? 0 : Math.max(0, v - 1)));

    }


    public void addBackground(BackgroundVector backgroundVector) {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(() -> {
            productCacheInfo.getRequestIdSet().addAsync(backgroundVector.getId());
            productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_BACKGROUND_COUNT, (k, v) -> v == null ? 1 : Math.max(1, v + 1));
            productCacheInfo.getIdProvinceCounter()
                .computeAsync(backgroundVector.getId_no_prov_nm(), (k, v) -> v == null ? 1 : Math.max(1, v + 1));
            productCacheInfo.getIpProvinceCounter()
                .computeAsync(backgroundVector.getIp_prov_cn(), (k, v) -> v == null ? 1 : Math.max(1, v + 1));
            productCacheInfo.getLiveProvinceCounter()
                .computeAsync(backgroundVector.getLive_province(), (k, v) -> v == null ? 1 : Math.max(1, v + 1));
            productCacheInfo.getMobileProvinceCounter()
                .computeAsync(backgroundVector.getMobile_prov_nm(), (k, v) -> v == null ? 1 : Math.max(1, v + 1));
        });
    }

    public void deleteBackground(BackgroundVector backgroundVector) {
        if (!isProductExist()) {
            return;
        }
        invokeUnderReadLock(() -> {
            productCacheInfo.getRequestIdSet().removeAsync(backgroundVector.getId());
            productCacheInfo.getVectorCounter().computeAsync(CACHE_KEY_BACKGROUND_COUNT, (k, v) -> v == null ? 0 : Math.max(0, v - 1));
            productCacheInfo.getIdProvinceCounter()
                .computeAsync(backgroundVector.getId_no_prov_nm(), (k, v) -> v == null ? 0 : Math.max(0, v - 1));
            productCacheInfo.getIpProvinceCounter()
                .computeAsync(backgroundVector.getIp_prov_cn(), (k, v) -> v == null ? 0 : Math.max(0, v - 1));
            productCacheInfo.getLiveProvinceCounter()
                .computeAsync(backgroundVector.getLive_province(), (k, v) -> v == null ? 0 : Math.max(0, v - 1));
            productCacheInfo.getMobileProvinceCounter()
                .computeAsync(backgroundVector.getMobile_prov_nm(), (k, v) -> v == null ? 0 : Math.max(0, v - 1));
        });
    }

    private boolean isMetaCacheExist() {
        return invokeUnderReadLock(() -> !productCacheInfo.getMetaMap().isEmpty());
    }

    public void clear() {
        invokeUnderWriteLock(() -> {
            List<RFuture<?>> futures = new ArrayList<>();
            // 删除所有缓存
            futures.add(productCacheInfo.getMetaMap().clearAsync());
            futures.add(productCacheInfo.getRequestIdSet().deleteAsync());
            futures.add(productCacheInfo.getVectorCounter().clearAsync());
            futures.add(productCacheInfo.getIdProvinceCounter().clearAsync());
            futures.add(productCacheInfo.getIpProvinceCounter().clearAsync());
            futures.add(productCacheInfo.getLiveProvinceCounter().clearAsync());
            futures.add(productCacheInfo.getMobileProvinceCounter().clearAsync());
            // 等待删除完成
            futures.stream().map(RFuture::toCompletableFuture).forEach(CompletableFuture::join);
        });

    }

    @SneakyThrows
    public boolean rebuildProductCache(boolean forceRebuild) {

        TimeUnit.MILLISECONDS.sleep(rebuildCacheDelay);

        return invokeUnderWriteLock(() -> {

            if (!forceRebuild && !productCacheInfo.getMetaMap().isEmpty()) {
                return true;
            }

            String backgroundCollectionName = collectionUtil.buildBackgroundCollectionName(productId);
            String sceneCollectionName = collectionUtil.buildSceneCollectionName(productId);
            String blackCollectionName = collectionUtil.buildBlackCollectionName(productId);

            CollectionService collectionService = serviceProvider.getCollectionService();

            VectorService<BackgroundVector> backgroundVectorService = serviceProvider.getVectorService(backgroundCollectionName,
                BackgroundVector.class);
            VectorService<SceneVector> sceneVectorService = serviceProvider.getVectorService(sceneCollectionName, SceneVector.class);
            VectorService<BlackVector> blackVectorService = serviceProvider.getVectorService(blackCollectionName, BlackVector.class);

            boolean backgroundCollectionExist = collectionService.isCollectionExist(backgroundCollectionName);
            if (!backgroundCollectionExist) {
                log.warn("背景图Collection不存在，请先调用createProduct接口，productId:{}", productId);
                return false;
            }

            boolean sceneCollectionExist = collectionService.isCollectionExist(sceneCollectionName);
            if (!sceneCollectionExist) {
                log.warn("场景图Collection不存在，请先调用createProduct接口，productId:{}", productId);
                return false;
            }

            boolean blackCollectionExist = collectionService.isCollectionExist(blackCollectionName);
            if (!blackCollectionExist) {
                log.warn("黑样本Collection不存在，请先调用createProduct接口，productId:{}", productId);
                return false;
            }

            log.info("重建Redis缓存开始，productId:{}", productId);

            // Redis批量操作
            List<RFuture<?>> futures = new ArrayList<>();

            RSetAsync<String> vectorIdSet = productCacheInfo.getRequestIdSet();
            RMapAsync<String, Object> metaMap = productCacheInfo.getMetaMap();
            RMapAsync<String, Long> vectorCounter = productCacheInfo.getVectorCounter();
            RMapAsync<String, Long> idProvinceCounter = productCacheInfo.getIdProvinceCounter();
            RMapAsync<String, Long> ipProvinceCounter = productCacheInfo.getIpProvinceCounter();
            RMapAsync<String, Long> liveProvinceCounter = productCacheInfo.getLiveProvinceCounter();
            RMapAsync<String, Long> mobileProvinceCounter = productCacheInfo.getMobileProvinceCounter();

            // 删除所有缓存

            futures.add(metaMap.clearAsync());
            futures.add(vectorIdSet.deleteAsync());
            futures.add(vectorCounter.clearAsync());
            futures.add(idProvinceCounter.clearAsync());
            futures.add(ipProvinceCounter.clearAsync());
            futures.add(liveProvinceCounter.clearAsync());
            futures.add(mobileProvinceCounter.clearAsync());

            futures.stream().map(RFuture::toCompletableFuture).forEach(CompletableFuture::join);

            futures.clear();

            // 查询Collection信息
            Collection backgroundCollection = collectionService.queryCollectionInfo(backgroundCollectionName);
            Collection sceneCollection = collectionService.queryCollectionInfo(sceneCollectionName);
            Collection blackCollection = collectionService.queryCollectionInfo(blackCollectionName);

            // 重建counter缓存
            log.info("重建counter缓存, backgroundCounter:{}, sceneCounter:{}, blackCounter:{}", backgroundCollection.getCount(),
                sceneCollection.getCount(), blackCollection.getCount());
            futures.add(vectorCounter.fastPutAsync(CACHE_KEY_BACKGROUND_COUNT, backgroundCollection.getCount()));
            futures.add(vectorCounter.fastPutAsync(CACHE_KEY_SCENE_COUNT, sceneCollection.getCount()));
            futures.add(vectorCounter.fastPutAsync(CACHE_KEY_BLACK_COUNT, blackCollection.getCount()));

            // 重建meta缓存
            JSONObject collectionMetaJson = JSONObject.parseObject(backgroundCollection.getMeta());
            CollectionMeta collectionMeta = collectionMetaJson.to(CollectionMeta.class);
            log.info("重建meta缓存:{}", collectionMetaJson);
            futures.add(metaMap.putAllAsync(collectionMetaJson));

            // 重建聚合缓存
            if (backgroundCollection.getCount() > 0) {

                List<String> fields = new ArrayList<>();
                fields.add(CACHE_KEY_LIVE_PROVINCE_NUMBER);
                fields.add(CACHE_KEY_ID_NO_PROVINCE_NUMBER);
                fields.add(CACHE_KEY_IP_NO_PROVINCE_NUMBER);
                fields.add(CACHE_KEY_MOBILE_PROVINCE_NUMBER);

                Map<String, Map<String, Long>> aggregations = backgroundVectorService.queryAggregations(fields,
                    Math.toIntExact(collectionMeta.getMaxCollectionSize()));

                Map<String, Long> idNoProvCount = aggregations.get(CACHE_KEY_ID_NO_PROVINCE_NUMBER);
                if (idNoProvCount != null && !idNoProvCount.isEmpty()) {
                    log.info("重建idNoProv缓存:{}", idNoProvCount);
                    futures.add(idProvinceCounter.putAllAsync(idNoProvCount));
                }

                Map<String, Long> ipProvCount = aggregations.get(CACHE_KEY_IP_NO_PROVINCE_NUMBER);
                if (ipProvCount != null && !ipProvCount.isEmpty()) {
                    log.info("重建ipProv缓存:{}", ipProvCount);
                    futures.add(ipProvinceCounter.putAllAsync(ipProvCount));
                }

                Map<String, Long> liveProvCount = aggregations.get(CACHE_KEY_LIVE_PROVINCE_NUMBER);
                if (liveProvCount != null && !liveProvCount.isEmpty()) {
                    log.info("重建liveProv缓存:{}", liveProvCount);
                    futures.add(liveProvinceCounter.putAllAsync(liveProvCount));
                }

                Map<String, Long> mobileProvCount = aggregations.get(CACHE_KEY_MOBILE_PROVINCE_NUMBER);
                if (mobileProvCount != null && !mobileProvCount.isEmpty()) {
                    log.info("重建mobileProv缓存:{}", mobileProvCount);
                    futures.add(mobileProvinceCounter.putAllAsync(mobileProvCount));
                }

                // 重建customerId缓存
                //TODO 内存注意！
                List<BackgroundVector> vectorIds = backgroundVectorService.queryVectorList(0,
                    Math.toIntExact(collectionMeta.getMaxCollectionSize()));
                log.info("重建customerId缓存:size:{}", vectorIds.size());
                if (!vectorIds.isEmpty()) {
                    futures.add(vectorIdSet.addAllAsync(vectorIds.stream().map(BackgroundVector::getId).collect(Collectors.toList())));
                }
            }

            log.info("重建Redis缓存结束，productId:{}", productId);

            return true;
        });
    }

    private String buildKey(String key) {
        return productId + key;
    }

    public void invokeUnderReadLock(Runnable runnable) {
        readLock.lock(lockLeaseTime, TimeUnit.MILLISECONDS);
        try {
            runnable.run();
        } finally {
            readLock.unlock();
        }
    }

    public void invokeUnderWriteLock(Runnable runnable) {
        writeLock.lock(lockLeaseTime, TimeUnit.MILLISECONDS);
        try {
            runnable.run();
        } finally {
            writeLock.unlock();
        }
    }

    public <R> R invokeUnderReadLock(Supplier<R> supplier) {
        readLock.lock(lockLeaseTime, TimeUnit.MILLISECONDS);
        try {
            return supplier.get();
        } finally {
            readLock.unlock();
        }
    }

    public <R> R invokeUnderWriteLock(Supplier<R> supplier) {
        writeLock.lock(lockLeaseTime, TimeUnit.MILLISECONDS);
        try {
            return supplier.get();
        } finally {
            writeLock.unlock();
        }
    }

    public void invokeUnderLock(Runnable runnable, String lockName, long leaseTime) {
        RLock lock = redisManager.getLock(buildKey(lockName) + CACHE_KEY_LOCK);
        lock.lock(lockLeaseTime, TimeUnit.MILLISECONDS);
        try {
            runnable.run();
        } finally {
            lock.unlock();
        }
    }


}
