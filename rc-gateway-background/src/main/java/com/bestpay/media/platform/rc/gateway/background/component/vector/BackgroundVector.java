package com.bestpay.media.platform.rc.gateway.background.component.vector;

import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BackgroundVector extends Vector {

    private String customer_no;

    private String customer_name;

    private String rpf_serial_no;

    private String cert_id;

    private String mobile_prov_nm;

    private String id_no_prov_nm;

    private String live_province;

    private String ip_prov_cn;

    private String noise;

    private String geo_info;

    private String product_id;

}
