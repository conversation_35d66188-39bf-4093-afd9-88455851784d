package com.bestpay.media.platform.rc.gateway.background.controller.entity.background;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchBackgroundResponseDTO {

    private String id;

    private String rpfserialno;

    private String certid;

    private String customername;

    private String customer_no;

    private String mobile_prov_nm;

    private String id_no_prov_nm;

    private String live_province;

    private String ip_prov_cn;

    private String productId;

    private Double score;

}
