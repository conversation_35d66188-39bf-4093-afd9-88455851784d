<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>rc-gateway</artifactId>
    <groupId>com.bestpay.media.platform</groupId>
    <version>0.3.8</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>rc-test</artifactId>
  <packaging>jar</packaging>
  <version>0.3.8</version>
  <dependencies>
    <dependency>
      <groupId>com.bestpay.media.platform</groupId>
      <artifactId>rc-common</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.bestpay.media.platform</groupId>
      <artifactId>rc-gateway-riskimage</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <java.version>1.8</java.version>
  </properties>
</project>
