package com.bestpay.media.platform.rc.test.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.PropertyPlaceholderHelper;

/**
 * @Author: 段然
 * @Date: 2024/9/11
 */
@Slf4j
public class StaticTest {

    public static void main(String[] args) {
        jsonCopyTest();
        placeholderTest();
    }

    private static void placeholderTest() {
        PropertyPlaceholderHelper propertyPlaceholderHelper = new PropertyPlaceholderHelper("\"@", "@\"");
        Map<String, String> map = new HashMap<>();
        map.put("placeholder", "world");
        String result = propertyPlaceholderHelper.replacePlaceholders("hello, \"@placeholder@\"", map::get);
        log.info("placeholder test result:{}", result);
    }

    private static void jsonCopyTest() {
        CollectionMeta meta = CollectionMeta.builder()
            .collectionType("test111")
            .build();
        CollectionMeta newMeta = JSON.to(CollectionMeta.class, JSONObject.from(meta));
        newMeta.setCollectionType("test222");

        log.info("json copy test result:{},{}", meta, newMeta);
    }

}
