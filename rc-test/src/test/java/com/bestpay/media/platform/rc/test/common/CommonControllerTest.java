package com.bestpay.media.platform.rc.test.common;

import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.common.controller.CommonController;
import com.bestpay.media.platform.rc.test.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Slf4j
@ActiveProfiles("background-test")
@SpringBootTest(classes = RiskImageApplication.class, webEnvironment = WebEnvironment.RANDOM_PORT)
public class CommonControllerTest {

    private final int localServerPort;
    private final CommonController commonController;

    public CommonControllerTest(@LocalServerPort int localServerPort, @Autowired CommonController commonController) {
        this.localServerPort = localServerPort;
        this.commonController = commonController;
    }

    @Test
    public void testOuterAccess() {
        final String url = "http://localhost:" + localServerPort + "/version";
        String version = HttpUtil.get(url);
        Assertions.assertEquals(commonController.version(), version);
        log.info("version access test pass, version: {}", version);
    }


}
