package com.bestpay.media.platform.rc.test.common;

import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.background.service.RiskImageService;
import com.bestpay.media.platform.rc.test.util.BackgroundPhotoUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
@Slf4j
@ActiveProfiles("background-test")
@SpringBootTest(classes = RiskImageApplication.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ModelTest {

    private final RiskImageService riskImageService;

    @Autowired
    public ModelTest(RiskImageService riskImageService) {
        this.riskImageService = riskImageService;
    }

    @Test
    public void extractTest() {
        String base64 = BackgroundPhotoUtil.getRandomPhoto();
        ExtractResult extract = riskImageService.extract(base64,"test");
        log.info("extract:{}", extract);
        Assertions.assertNotNull(extract);
        Assertions.assertNotNull(extract.getNoise());
        Assertions.assertNotNull(extract.getImg_feature());
        Assertions.assertFalse(extract.getImg_feature().isEmpty());
        Assertions.assertEquals(768, extract.getImg_feature().size());
    }

}
