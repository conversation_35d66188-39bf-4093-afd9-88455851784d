package com.bestpay.media.platform.rc.test.util;

import com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant;
import com.fasterxml.jackson.databind.json.JsonMapper;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * @Author: 段然
 * @Date: 2024/4/9
 */
@UtilityClass
public class HttpUtil {

    private static final OkHttpClient CLIENT = new OkHttpClient().newBuilder()
        .readTimeout(60, TimeUnit.SECONDS)
        .callTimeout(60, TimeUnit.SECONDS)
        .connectTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build();

    @SneakyThrows
    public static String get(String url) {
        MediaType mediaType = MediaType.parse(RiskImageConstant.CONTENT_TYPE_JSON);
        Request request = new Builder()
            .url(url)
            .get()
            .build();
        try (Response response = CLIENT.newCall(request).execute()) {
            return response.body() != null ? response.body().string() : null;
        }
    }

    @SneakyThrows
    public static String post(String url, Object body) {
        JsonMapper jsonMapper = new JsonMapper();
        String bodyJson = jsonMapper.writeValueAsString(body);
        MediaType mediaType = MediaType.parse(RiskImageConstant.CONTENT_TYPE_JSON);
        Request request = new Builder()
            .url(url)
            .post(RequestBody.create(bodyJson, mediaType))
            .build();
        try (Response response = CLIENT.newCall(request).execute()) {
            return response.body() != null ? response.body().string() : null;
        }
    }
}
