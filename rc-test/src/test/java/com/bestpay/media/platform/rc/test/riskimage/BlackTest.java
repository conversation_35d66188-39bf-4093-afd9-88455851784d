package com.bestpay.media.platform.rc.test.riskimage;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.AddBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.DeleteBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.QueryBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.black.SearchBlackResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.AddProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.DeleteProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.test.util.BackgroundPhotoUtil;
import com.bestpay.media.platform.rc.test.util.HttpUtil;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/9
 */
@Slf4j
@ActiveProfiles("background-test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(classes = RiskImageApplication.class, webEnvironment = WebEnvironment.RANDOM_PORT)
public class BlackTest {

    private final static String PRODUCT_ID = "unit_test_black_" + RandomStringUtils.randomAlphabetic(8).toLowerCase();

    private final static String ID = RandomStringUtils.randomAlphabetic(8).toLowerCase();

    private final static String LIVE_PROVINCE_NOT_EXISTS = "上海";

    private final static String LIVE_PROVINCE = "云南";

    private final String httpLocalhost;

    @SneakyThrows
    public BlackTest(@LocalServerPort int localServerPort) {
        this.httpLocalhost = "http://localhost:" + localServerPort;
    }

    @Test
    @Order(10)
    @SneakyThrows
    public void addProductIfNotExistTest() {
        QueryProductRequestDTO queryRequest = new QueryProductRequestDTO();
        queryRequest.setRequestId(UUID.randomUUID().toString());
        Response<List<QueryProductResponseDTO>> queryResponse = sendRequest(queryRequest);
        Assertions.assertNotNull(queryResponse);
        Assertions.assertTrue(queryResponse.getSuccess());
        Assertions.assertEquals(queryResponse.getCode(), 200);
        if (queryResponse.getResult().stream().noneMatch(e -> e.getProductId().equals(PRODUCT_ID))) {
            AddProductRequestDTO addRequest = new AddProductRequestDTO();
            addRequest.setProductId(PRODUCT_ID);
            addRequest.setRequestId(UUID.randomUUID().toString());
            Response<Boolean> addResponse = sendRequest(addRequest);
            Assertions.assertNotNull(addResponse);
            Assertions.assertTrue(addResponse.getSuccess());
            Assertions.assertEquals(addResponse.getCode(), 200);
            Assertions.assertTrue(addResponse.getResult());
        }
    }

    @Test
    @Order(100)
    @SneakyThrows
    public void addBlackTest() {
        String randomPhoto = BackgroundPhotoUtil.getRandomPhoto();
        AddBlackRequestDTO request = AddBlackRequestDTO.builder()
            .id(ID)
            .productId(PRODUCT_ID)
            .base64(randomPhoto)
            .requestId(UUID.randomUUID().toString())
            .liveProvince(LIVE_PROVINCE)
            .build();
        Response<String> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertEquals(response.getResult(), ID);
        TimeUnit.SECONDS.sleep(1);
    }

    @Test
    @Order(101)
    @SneakyThrows
    public void queryBlackTest() {
        QueryBlackRequestDTO request = QueryBlackRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .build();
        Response<List<QueryBlackResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertFalse(response.getResult().isEmpty());
        Assertions.assertTrue(response.getResult().stream().anyMatch(e -> e.getId().equals(ID)));
        Assertions.assertTrue(response.getResult().stream().anyMatch(e -> e.getLiveProvince().equals(LIVE_PROVINCE)));
    }

    @Test
    @Order(102)
    @SneakyThrows
    public void searchBlackTest() {
        String randomPhoto = BackgroundPhotoUtil.getRandomPhoto();
        SearchBlackRequestDTO request = SearchBlackRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .base64(randomPhoto)
            .threshold(0.0F)
            .topN(10)
            .liveProvince(LIVE_PROVINCE)
            .build();
        Response<List<SearchBlackResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertFalse(response.getResult().isEmpty());
    }

    @Test
    @Order(103)
    @SneakyThrows
    public void searchBlackTestLiveProvinceNotExits() {
        String randomPhoto = BackgroundPhotoUtil.getRandomPhoto();
        SearchBlackRequestDTO request = SearchBlackRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .base64(randomPhoto)
            .threshold(0.0F)
            .topN(10)
            .liveProvince(LIVE_PROVINCE_NOT_EXISTS)
            .build();
        Response<List<SearchBlackResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult().isEmpty());
    }

    @Test
    @Order(109)
    @SneakyThrows
    public void deleteBlackTest() {
        DeleteBlackRequestDTO request = DeleteBlackRequestDTO.builder()
            .id(ID)
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .build();
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult());
        TimeUnit.SECONDS.sleep(1);
    }

    @Test
    @Order(110)
    @SneakyThrows
    public void queryBlackNotExistTest() {
        QueryBlackRequestDTO request = QueryBlackRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .build();
        Response<List<QueryBlackResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult().isEmpty());
    }

    @Test
    @Order(120)
    @SneakyThrows
    public void searchBlackNotExistTest() {
        String randomPhoto = BackgroundPhotoUtil.getRandomPhoto();
        SearchBlackRequestDTO request = SearchBlackRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .base64(randomPhoto)
            .topN(10)
            .threshold(0.0F)
            .liveProvince(LIVE_PROVINCE)
            .build();
        Response<List<SearchBlackResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult().isEmpty());
    }

    @Test
    @Order(999)
    @SneakyThrows
    public void deleteProductTest() {
        DeleteProductRequestDTO request = new DeleteProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult());
    }


    private Response<String> sendRequest(AddBlackRequestDTO request) {
        final String url = httpLocalhost + "/addBlack";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<String>>() {
        });
    }

    private Response<Boolean> sendRequest(DeleteBlackRequestDTO request) {
        final String url = httpLocalhost + "/deleteBlack";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }

    private Response<List<QueryBlackResponseDTO>> sendRequest(QueryBlackRequestDTO request) {
        final String url = httpLocalhost + "/queryBlack";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<QueryBlackResponseDTO>>>() {
        });
    }

    private Response<List<SearchBlackResponseDTO>> sendRequest(SearchBlackRequestDTO request) {
        final String url = httpLocalhost + "/searchBlack";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<SearchBlackResponseDTO>>>() {
        });
    }


    private Response<List<QueryProductResponseDTO>> sendRequest(QueryProductRequestDTO request) {
        final String url = httpLocalhost + "/queryProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<QueryProductResponseDTO>>>() {
        });
    }

    private Response<Boolean> sendRequest(AddProductRequestDTO request) {
        final String url = httpLocalhost + "/addProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }

    private Response<Boolean> sendRequest(DeleteProductRequestDTO request) {
        final String url = httpLocalhost + "/deleteProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }


}
