package com.bestpay.media.platform.rc.test.common;

import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_ID_NO_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_IP_NO_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_LIVE_PROVINCE_NUMBER;
import static com.bestpay.media.platform.rc.gateway.background.component.RiskImageConstant.CACHE_KEY_MOBILE_PROVINCE_NUMBER;

import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.background.component.CollectionMeta;
import com.bestpay.media.platform.rc.gateway.background.component.vector.BackgroundVector;
import com.bestpay.media.platform.rc.gateway.background.util.CollectionUtil;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import com.bestpay.media.platform.rc.gateway.common.vector.service.CollectionService;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
@Slf4j
@ActiveProfiles("background-test")
@SpringBootTest(classes = RiskImageApplication.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class VectorEngineTest {

    private final static int VECTOR_NUM = 100;
    private static final int SEARCH_TIMES = 15;
    private static final int DELETE_TIMES = 15;
    private static final int QUERY_OLDEST_OFFSET = 45;
    private static final String COLLECTION_NAME = "unit_test_" + RandomStringUtils.randomAlphabetic(8).toLowerCase();
    private static final String META_VALUE = "random_meta_" + RandomStringUtils.randomAlphabetic(8).toUpperCase();
    private static final List<BackgroundVector> RANDOM_VECTORS = generateRandomVectors(768, VECTOR_NUM);

    private final String collectionName;

    private final CollectionUtil collectionUtil;

    private final CollectionService collectionService;

    private final VectorEngineServiceProvider serviceProvider;

    private final VectorService<BackgroundVector> vectorService;

    @Autowired
    public VectorEngineTest(CollectionUtil collectionUtil, VectorEngineServiceProvider serviceProvider) {
        this.collectionUtil = collectionUtil;
        this.serviceProvider = serviceProvider;
        this.collectionService = serviceProvider.getCollectionService();
        this.collectionName = collectionUtil.buildBackgroundCollectionName(COLLECTION_NAME);
        this.vectorService = serviceProvider.getVectorService(collectionName, BackgroundVector.class);
    }

    @Test
    @Order(90)
    @SneakyThrows
    public void collectionNotFoundTest() {
        GateWayException exception =
            Assertions.assertThrowsExactly(GateWayException.class, () -> collectionService.queryCollectionInfo(collectionName));
        Assertions.assertTrue(exception.getMessage().contains("特征库不存在，请检查特征库是否存在"));
        log.info("Collection not found test pass");
    }

    @Test
    @Order(91)
    @SneakyThrows
    public void createCollectionInvalidNameTest() {
        CollectionMeta collectionMeta = CollectionMeta.builder()
            .productId(COLLECTION_NAME)
            .description(META_VALUE)
            .build();
        CollectionParam collectionParam = collectionUtil.buildBackgroundCollectionParams(collectionMeta);
        GateWayException exception = Assertions.assertThrowsExactly(GateWayException.class,
            () -> collectionService.createCollection("!@#$%^&()QAZ123", collectionParam));
        assertThrowsMessage(exception, "特征库名称collectionName无效");
        log.info("Create collection invalid name test pass");
    }


    @Test
    @Order(110)
    @SneakyThrows
    public void createCollectionTest() {
        CollectionMeta collectionMeta = CollectionMeta.builder()
            .productId(COLLECTION_NAME)
            .description(META_VALUE)
            .build();
        CollectionParam collectionParam = collectionUtil.buildBackgroundCollectionParams(collectionMeta);
        boolean collection = collectionService.createCollection(collectionName, collectionParam);
        Assertions.assertTrue(collection);
        log.info("Create collection test pass");
    }


    @Test
    @Order(111)
    @SneakyThrows
    public void createCollectionAlreadyExistTest() {
        CollectionMeta collectionMeta = CollectionMeta.builder()
            .productId(COLLECTION_NAME)
            .description(META_VALUE)
            .build();
        CollectionParam collectionParam = collectionUtil.buildBackgroundCollectionParams(collectionMeta);
        GateWayException exception = Assertions.assertThrowsExactly(GateWayException.class,
            () -> collectionService.createCollection(collectionName, collectionParam));
        assertThrowsMessage(exception, "特征库已存在");
        log.info("Create collection already exist test pass");
    }


    @Test
    @Order(120)
    @SneakyThrows
    public void getCollectionListTest() {
        List<Collection> collections = collectionService.queryCollectionInfoList();
        Collection collection = collections.stream().filter(c -> collectionName.equals(c.getName()))
            .findFirst().orElse(null);
        Assertions.assertNotNull(collection);
        Assertions.assertEquals(collectionName, collection.getName());
        Assertions.assertEquals(0, collection.getCount());
        log.info("Get collection list test pass");
    }

    @Test
    @Order(130)
    @SneakyThrows
    public void isCollectionExistTest() {
        boolean collectionExist = collectionService.isCollectionExist(collectionName);
        Assertions.assertTrue(collectionExist);
        boolean shouldNotExist = collectionService.isCollectionExist("NotExist");
        Assertions.assertFalse(shouldNotExist);
        log.info("Collection exist test pass");
    }

    @Test
    @Order(210)
    @SneakyThrows
    public void insertVectorTest() {
        for (int i = 0, vectorsSize = RANDOM_VECTORS.size(); i < vectorsSize; i++) {
            BackgroundVector vector = RANDOM_VECTORS.get(i);
            String id = vectorService.insertVector(vector);
            Assertions.assertNotNull(id);
            Assertions.assertEquals(vector.getId(), id);
            log.info("Insert vector {} / {}", i, vectorsSize);
        }
        TimeUnit.SECONDS.sleep(1);
        Collection collectionInfo = collectionService.queryCollectionInfo(collectionName);
        Assertions.assertEquals(VECTOR_NUM, collectionInfo.getCount());
        log.info("Insert vector test pass");
    }

    @Test
    @Order(220)
    @SneakyThrows
    public void vectorExistTest() {
        RANDOM_VECTORS.forEach(vector -> {
            boolean exist = vectorService.isVectorExistById(vector.getId());
            Assertions.assertTrue(exist);
        });
        log.info("Vector exist test pass");
    }

    @Test
    @Order(230)
    @SneakyThrows
    public void queryVectorTest() {
        List<BackgroundVector> vectorIdList = vectorService.queryVectorList(0,
            VECTOR_NUM * 2);
        Assertions.assertEquals(VECTOR_NUM, vectorIdList.size());
        Set<String> vectorSet = RANDOM_VECTORS.stream().map(Vector::getId).collect(Collectors.toSet());
        vectorIdList.forEach(vector -> {
            boolean contains = vectorSet.contains(vector.getId());
            Assertions.assertTrue(contains);
        });
        log.info("Query vector test pass");
    }

    @Test
    @Order(240)
    @SneakyThrows
    public void vectorSearchTest() {
        int randomIndex = RandomUtils.nextInt(0, VECTOR_NUM - SEARCH_TIMES);
        for (int i = randomIndex; i < randomIndex + SEARCH_TIMES; i++) {
            Vector vector = RANDOM_VECTORS.get(i);
            int randomK = RandomUtils.nextInt(5, 10);
            List<Float> floats = vector.getVector();
            floats.set(randomIndex, floats.get(randomIndex) / 2f);
            SearchParam searchParam = SearchParam.builder()
                .vector(vector.getVector())
                .topK(randomK)
                .build();
            List<SearchResult<BackgroundVector>> searchResultVectors = vectorService.searchVector(searchParam);
            Assertions.assertEquals(randomK, searchResultVectors.size());
            Assertions.assertEquals(searchResultVectors.get(0).getVector().getId(), vector.getId());
            Assertions.assertTrue(searchResultVectors.get(0).getScore() > 0.9);
        }
        log.info("Vector search test pass");
    }


    @Test
    @Order(250)
    @SneakyThrows
    public void deleteVectorTest() {
        for (int i = 0; i < DELETE_TIMES; i++) {
            Vector vector = RANDOM_VECTORS.get(i);
            boolean delete = vectorService.deleteVectorById(vector.getId());
            Assertions.assertTrue(delete);
        }
        TimeUnit.SECONDS.sleep(1);
        Collection collectionInfo = collectionService.queryCollectionInfo(collectionName);
        Assertions.assertEquals(VECTOR_NUM - DELETE_TIMES, collectionInfo.getCount());
        log.info("Delete vector test pass");
    }

    @Test
    @Order(260)
    @SneakyThrows
    public void queryOldestVectorsTest() {
        List<BackgroundVector> oldestVectors = vectorService.queryOldestVectors(QUERY_OLDEST_OFFSET,
            VECTOR_NUM - QUERY_OLDEST_OFFSET - DELETE_TIMES);
        Assertions.assertEquals(VECTOR_NUM - QUERY_OLDEST_OFFSET - DELETE_TIMES, oldestVectors.size());
        int i = VECTOR_NUM - QUERY_OLDEST_OFFSET - 1;
        for (BackgroundVector oldestVector : oldestVectors) {
            BackgroundVector vector = RANDOM_VECTORS.get(i);
            Assertions.assertEquals(vector.getId(), oldestVector.getId());
            i--;
        }
        log.info("Query oldest vectors test pass");
    }


    @Test
    @Order(270)
    @SneakyThrows
    public void queryAggregationsTest() {
        List<String> fields = new ArrayList<>();
        fields.add(CACHE_KEY_LIVE_PROVINCE_NUMBER);
        fields.add(CACHE_KEY_ID_NO_PROVINCE_NUMBER);
        fields.add(CACHE_KEY_IP_NO_PROVINCE_NUMBER);
        fields.add(CACHE_KEY_MOBILE_PROVINCE_NUMBER);
        Map<String, Map<String, Long>> aggregations = vectorService.queryAggregations(fields, VECTOR_NUM);
        Map<String, Long> aggregationCount = new HashMap<>();
        List<Pair<String, Long>> fromLocal = new ArrayList<>();
        List<Pair<String, Long>> fromES = new ArrayList<>();
        for (int i = DELETE_TIMES; i < RANDOM_VECTORS.size(); i++) {
            BackgroundVector vector = RANDOM_VECTORS.get(i);
            aggregationCount.merge(vector.getLive_province(), 1L, Long::sum);
        }
        aggregations.get(CACHE_KEY_LIVE_PROVINCE_NUMBER).forEach((k, v) -> fromES.add(Pair.of(k, v)));
        aggregationCount.forEach((k, v) -> fromLocal.add(Pair.of(k, v)));
        fromES.sort(Comparator.comparing(Pair::getLeft));
        fromLocal.sort(Comparator.comparing(Pair::getLeft));
        Assertions.assertEquals(fromLocal, fromES);
        log.info("Query aggregations test pass");
    }


    @Test
    @Order(310)
    @SneakyThrows
    public void getCollectionTest() {
        Collection collectionInfo = collectionService.queryCollectionInfo(collectionName);
        String metaJson = collectionInfo.getMeta();
        CollectionMeta meta = JSONObject.parseObject(metaJson, CollectionMeta.class);
        Assertions.assertEquals(META_VALUE, meta.getDescription());
        Assertions.assertEquals(collectionName, collectionInfo.getName());
        Assertions.assertEquals(VECTOR_NUM - DELETE_TIMES, collectionInfo.getCount());
        log.info("Get collection test pass");
    }


    @Test
    @Order(320)
    @SneakyThrows
    public void deleteCollectionTest() {
        boolean collection = collectionService.deleteCollection(collectionName);
        Assertions.assertTrue(collection);
        boolean collectionExist = collectionService.isCollectionExist(collectionName);
        Assertions.assertFalse(collectionExist);
        log.info("Delete collection test pass");
    }


    @SneakyThrows
    private static List<BackgroundVector> generateRandomVectors(int dim, int num) {
        List<BackgroundVector> vectors = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            BackgroundVector vector = new BackgroundVector();
            vector.setId(RandomStringUtils.randomAlphabetic(8) + "_" + i);
            vector.setVector(new ArrayList<>(dim));
            for (int j = 0; j < dim; j++) {
                vector.getVector().add(RandomUtils.nextFloat(0, 1));
            }
            vector.setCreation_date(System.currentTimeMillis());
            vector.setLive_province(RandomStringUtils.randomAlphabetic(1));
            vector.setId_no_prov_nm(RandomStringUtils.randomAlphabetic(1));
            vector.setIp_prov_cn(RandomStringUtils.randomAlphabetic(1));
            vector.setMobile_prov_nm(RandomStringUtils.randomAlphabetic(1));
            vector.setCreation_date(System.currentTimeMillis());
            vectors.add(vector);
            TimeUnit.MILLISECONDS.sleep(1);
        }
        return vectors;
    }


    private void assertThrowsMessage(Throwable throwable, String message) {
        boolean messageMatched = throwable.getMessage().contains(message);
        boolean causeMessageMatched = throwable.getCause() != null && throwable.getCause().getMessage().contains(message);
        Assertions.assertTrue(messageMatched || causeMessageMatched);
    }
}
