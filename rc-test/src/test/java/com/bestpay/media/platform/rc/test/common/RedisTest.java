package com.bestpay.media.platform.rc.test.common;

import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.redis.component.FairSemaphore;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Slf4j
@ActiveProfiles("background-test")
@SpringBootTest(classes = RiskImageApplication.class)
public class RedisTest {

    private final RedisManager redisManager;

    @Autowired
    public RedisTest(RedisManager redisManager) {
        this.redisManager = redisManager;
    }

    @Test
    @SneakyThrows
    public void fairSemaphoreTest() {
        log.info("fairSemaphore test start");
        final int testCount = 10;
        CountDownLatch countDownLatch = new CountDownLatch(testCount);
        FairSemaphore fairSemaphore = redisManager.getFairSemaphore(
            "UnitTest_" + RandomStringUtils.randomAlphabetic(8));
        ExecutorService threadPool = Executors.newFixedThreadPool(testCount);
        fairSemaphore.trySetPermits(1);
        List<Integer> syncList = new ArrayList<>(testCount);
        List<Integer> asyncList = new ArrayList<>(testCount);
        for (int i = 0; i < testCount; i++) {
            Integer order = i + 1;
            threadPool.submit(() -> {
                String permitId = null;
                try {
                    log.info("fairSemaphore test thread {} start", order);
                    permitId = fairSemaphore.acquire();
                    log.info("fairSemaphore test thread {} acquired permit", order);
                    asyncList.add(order);
                    TimeUnit.MILLISECONDS.sleep(50);
                    log.info("fairSemaphore test thread {} work done", order);
                } catch (Exception e) {
                    log.info("fairSemaphore test thread {} error", order, e);
                } finally {
                    fairSemaphore.release(permitId);
                    countDownLatch.countDown();
                    log.info("fairSemaphore test thread {} released permit", order);
                }
            });
            syncList.add(order);
            TimeUnit.MILLISECONDS.sleep(10);
        }
        countDownLatch.await();
        log.info("fairSemaphore test finished, execution order:{}", Arrays.toString(asyncList.toArray()));
        Assertions.assertEquals(testCount, syncList.size());
        Assertions.assertArrayEquals(syncList.toArray(), asyncList.toArray());
    }
}
