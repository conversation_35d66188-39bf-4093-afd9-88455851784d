package com.bestpay.media.platform.rc.test.util;

import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO.RiskImageData;
import java.io.File;
import java.util.Base64;
import java.util.UUID;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.apache.commons.io.FileUtils;

/**
 * @Author: 段然
 * @Date: 2024/9/14
 */
@UtilityClass
public class BackgroundPhotoUtil {

    private final static String PHOTO_PATH = "D:\\Document\\侦图\\risk-face\\background_test";

    @SneakyThrows
    public static String getRandomPhoto() {
        File path = new File(PHOTO_PATH);
        File[] files = path.listFiles();
        if (files == null || files.length == 0) {
            throw new RuntimeException("No photo found in " + PHOTO_PATH);
        }
        File file = files[(int) (Math.random() * files.length)];
        byte[] bytes = FileUtils.readFileToByteArray(file);
        return Base64.getEncoder().encodeToString(bytes);
    }


    @SneakyThrows
    public static RiskImageRequestDTO generateRiskImageRequest(String productId) {

        String random = UUID.randomUUID().toString();

        String province = "云南";

        RiskImageData data = new RiskImageData();
        data.setBase64(BackgroundPhotoUtil.getRandomPhoto());
        data.setProductId(productId);
        data.setRpfserialno("rpfserialno_" + random);
        data.setCertid("certid_" + random);
        data.setCustomername("customername_" + random);
        data.setCustomer_no("customer_no_" + random);
        data.setMobile_prov_nm(province);
        data.setIp_prov_cn(null);
        data.setId_no_prov_nm("");
        data.setLive_province(province);

        RiskImageRequestDTO request = new RiskImageRequestDTO();
        request.setData(data);

        return request;
    }


}
