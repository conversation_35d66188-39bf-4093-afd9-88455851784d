package com.bestpay.media.platform.rc.test.riskimage;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.AnalysisResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.RiskImageRequestDTO.RiskImageData;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.background.SearchBackgroundResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.AddProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.DeleteProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductResponseDTO;
import com.bestpay.media.platform.rc.gateway.background.model.entity.ExtractResponseDTO.ExtractResult;
import com.bestpay.media.platform.rc.gateway.background.service.RiskImageService;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.test.util.BackgroundPhotoUtil;
import com.bestpay.media.platform.rc.test.util.HttpUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/9
 */
@Slf4j
@ActiveProfiles("background-test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(classes = RiskImageApplication.class, webEnvironment = WebEnvironment.RANDOM_PORT)
public class BackgroundTest {

    private final static String PRODUCT_ID = "unit_test_risk_image_" + RandomStringUtils.randomAlphabetic(8).toLowerCase();

    private final static List<RiskImageRequestDTO> RANDOM_REQUESTS = generateRandomRequests(PRODUCT_ID, 10);

    private final RiskImageService riskImageService;

    private final String httpLocalhost;

    @SneakyThrows
    public BackgroundTest(@Autowired RiskImageService riskImageService, @LocalServerPort int localServerPort) {
        this.riskImageService = riskImageService;
        this.httpLocalhost = "http://localhost:" + localServerPort;
    }

    @Test
    @Order(10)
    @SneakyThrows
    public void addProductIfNotExist() {
        QueryProductRequestDTO queryRequest = new QueryProductRequestDTO();
        queryRequest.setRequestId(UUID.randomUUID().toString());
        Response<List<QueryProductResponseDTO>> queryResponse = sendRequest(queryRequest);
        Assertions.assertNotNull(queryResponse);
        Assertions.assertTrue(queryResponse.getSuccess());
        Assertions.assertEquals(queryResponse.getCode(), 200);
        if (queryResponse.getResult().stream().noneMatch(e -> e.getProductId().equals(PRODUCT_ID))) {
            AddProductRequestDTO addRequest = new AddProductRequestDTO();
            addRequest.setProductId(PRODUCT_ID);
            addRequest.setRequestId(UUID.randomUUID().toString());
            Response<Boolean> addResponse = sendRequest(addRequest);
            Assertions.assertNotNull(addResponse);
            Assertions.assertTrue(addResponse.getSuccess());
            Assertions.assertEquals(addResponse.getCode(), 200);
            Assertions.assertTrue(addResponse.getResult());
        }
    }

    @Test
    @Order(100)
    @SneakyThrows
    public void riskImageTest() {
        RiskImageRequestDTO request = RANDOM_REQUESTS.get(0);
        Response<String> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
    }

    @Test
    @Order(101)
    @SneakyThrows
    public void riskImageInsertOnlyTest() {
        RiskImageRequestDTO request = RANDOM_REQUESTS.get(1);
        request.getData().setInsert_only("true");
        Response<String> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getHelp().contains("当insert_only为true时，只插入背景照，不进行计算"));
    }

    @Test
    @Order(102)
    @SneakyThrows
    public void riskImageFutureOnlyTest() {
        RiskImageRequestDTO request = RANDOM_REQUESTS.get(2);
        RiskImageData data = request.getData();
        data.setFeature_only("true");
        String base64 = data.getBase64();
        data.setBase64(null);
        ExtractResult extract = riskImageService.extract(base64, data.getCustomer_no());
        data.setImg_feature(extract.getImg_feature());
        data.setNoise(extract.getNoise());
        Response<String> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
    }

    @Test
    @Order(103)
    @SneakyThrows
    public void riskImageInsertOnlyAndFutureOnlyTest() {
        RiskImageRequestDTO request = RANDOM_REQUESTS.get(3);
        RiskImageData data = request.getData();
        data.setFeature_only("true");
        data.setInsert_only("true");
        String base64 = data.getBase64();
        data.setBase64(null);
        ExtractResult extract = riskImageService.extract(base64, data.getCustomer_no());
        data.setImg_feature(extract.getImg_feature());
        data.setNoise(extract.getNoise());
        Response<String> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getHelp().contains("当insert_only为true时，只插入背景照，不进行计算"));
    }

    @Test
    @Order(200)
    @SneakyThrows
    public void searchImageTest() {
        RiskImageRequestDTO riskImage = RANDOM_REQUESTS.get(0);
        SearchBackgroundRequestDTO request = SearchBackgroundRequestDTO.builder()
            .topN(10)
            .base64(BackgroundPhotoUtil.getRandomPhoto())
            .productId(PRODUCT_ID)
            .liveProvince(riskImage.getData().getLive_province())
            .threshold(0.0F)
            .requestId(UUID.randomUUID().toString())
            .build();
        Response<List<SearchBackgroundResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertNotNull(response.getResult());
        Assertions.assertFalse(response.getResult().isEmpty());
        response.getResult().forEach(e -> {
            Assertions.assertEquals(e.getProductId(), PRODUCT_ID);
            Assertions.assertEquals(e.getLive_province(), riskImage.getData().getLive_province());
        });
    }

    @Test
    @Order(200)
    @SneakyThrows
    public void analysisTest() {
        AnalysisRequestDTO request = AnalysisRequestDTO.builder()
            .productId(PRODUCT_ID)
            .requestId(UUID.randomUUID().toString())
            .build();
        Response<AnalysisResponseDTO> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertNotNull(response.getResult());
        Assertions.assertFalse(response.getResult().getIdNoProvInfo().isEmpty());
        Assertions.assertFalse(response.getResult().getIpProvCnInfo().isEmpty());
        Assertions.assertFalse(response.getResult().getMobileProvInfo().isEmpty());
        Assertions.assertFalse(response.getResult().getLiveProvInfo().isEmpty());
    }


    @Test
    @Order(999)
    @SneakyThrows
    public void deleteProductTest() {
        DeleteProductRequestDTO request = new DeleteProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult());
    }


    private Response<String> sendRequest(RiskImageRequestDTO request) {
        final String url = httpLocalhost + "/riskImage";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<String>>() {
        });
    }

    private Response<List<QueryProductResponseDTO>> sendRequest(QueryProductRequestDTO request) {
        final String url = httpLocalhost + "/queryProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<QueryProductResponseDTO>>>() {
        });
    }

    private Response<Boolean> sendRequest(AddProductRequestDTO request) {
        final String url = httpLocalhost + "/addProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }

    private Response<Boolean> sendRequest(DeleteProductRequestDTO request) {
        final String url = httpLocalhost + "/deleteProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }

    private Response<List<SearchBackgroundResponseDTO>> sendRequest(SearchBackgroundRequestDTO request) {
        final String url = httpLocalhost + "/searchImage";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<SearchBackgroundResponseDTO>>>() {
        });
    }

    private Response<AnalysisResponseDTO> sendRequest(AnalysisRequestDTO request) {
        final String url = httpLocalhost + "/analysis";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<AnalysisResponseDTO>>() {
        });
    }

    private static List<RiskImageRequestDTO> generateRandomRequests(String productId, int num) {
        List<RiskImageRequestDTO> requests = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            requests.add(BackgroundPhotoUtil.generateRiskImageRequest(productId));
        }
        return requests;
    }
}
