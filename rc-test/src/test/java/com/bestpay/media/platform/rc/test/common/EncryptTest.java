package com.bestpay.media.platform.rc.test.common;

import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.common.component.BlowfishComponent;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
@Slf4j
@ActiveProfiles("background-test")
@SpringBootTest(classes = RiskImageApplication.class)
public class EncryptTest {

  private final BlowfishComponent blowfishComponent;

  @Autowired
  public EncryptTest(BlowfishComponent blowfishComponent) {
    this.blowfishComponent = blowfishComponent;
  }

  @Test
  public void blowFishEncryptTest() {
    String origin = "ABCdef123!@#测试";
    String encrypted = blowfishComponent.encrypt(origin);
    String decrypted = blowfishComponent.decrypt(encrypted);
    Assertions.assertEquals(origin, decrypted);
    log.info("blowFishEncryptTest pass, origin: {}, encrypted: {}, decrypted: {}", origin, encrypted, decrypted);
  }

  @Test
  public void blowFishEncryptCompatibilityTest() {
    String encrypted = "ug+dKZSHA3fu2ObeBSkgSJcphhjvzlHG";
    String origin = "ABCdef123!@#测试";
    String decrypted = blowfishComponent.decrypt(encrypted);
    Assertions.assertEquals(origin, decrypted);
    log.info("blowFishEncryptCompatibilityTest pass, origin: {}, encrypted: {}, decrypted: {}", origin, encrypted, decrypted);
  }

}
