package com.bestpay.media.platform.rc.test.riskimage;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.bestpay.media.platform.rc.gateway.background.RiskImageApplication;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.AddProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.DeleteProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductRequestDTO;
import com.bestpay.media.platform.rc.gateway.background.controller.entity.product.QueryProductResponseDTO;
import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.test.util.HttpUtil;
import java.util.List;
import java.util.UUID;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Author: 段然
 * @Date: 2024/4/9
 */
@Slf4j
@ActiveProfiles("background-test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(classes = RiskImageApplication.class, webEnvironment = WebEnvironment.RANDOM_PORT)
public class ProductManageTest {

    private final static String PRODUCT_ID = "unit_test_product_" + RandomStringUtils.randomAlphabetic(8).toLowerCase();

    private final String httpLocalhost;

    @SneakyThrows
    public ProductManageTest(@LocalServerPort int localServerPort) {
        this.httpLocalhost = "http://localhost:" + localServerPort;
    }

    @Test
    @Order(100)
    @SneakyThrows
    public void addProductTest() {
        AddProductRequestDTO request = new AddProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult());
    }

    @Test
    @Order(101)
    @SneakyThrows
    public void addProductAlreadyExistTest() {
        AddProductRequestDTO request = new AddProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 400);
        Assertions.assertEquals(response.getMessage(), "请求参数错误");
    }

    @Test
    @Order(102)
    @SneakyThrows
    public void addProductInvalidNameTest() {
        AddProductRequestDTO request = new AddProductRequestDTO();
        request.setProductId("Inva l i D _n342am DSAe 无效库名 !@#$%^&*()");
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 400);
        Assertions.assertEquals(response.getMessage(), "请求参数错误");
    }

    @Test
    @Order(104)
    @SneakyThrows
    public void addProductTooLongCollectionNameTest() {
        AddProductRequestDTO request = new AddProductRequestDTO();
        request.setProductId(RandomStringUtils.randomAlphabetic(4096).toLowerCase());
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 400);
        Assertions.assertEquals(response.getMessage(), "请求参数错误");
    }


    @Test
    @Order(200)
    @SneakyThrows
    public void queryProductTest() {
        QueryProductRequestDTO request = new QueryProductRequestDTO();
        request.setRequestId(UUID.randomUUID().toString());
        Response<List<QueryProductResponseDTO>> response = sendRequest(request);
        log.info("query product response: {}", JSON.toJSONString(response));
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertFalse(response.getResult().isEmpty());
        Assertions.assertTrue(response.getResult().stream().anyMatch(e -> e.getProductId().equals(PRODUCT_ID)));
    }

    @Test
    @Order(300)
    @SneakyThrows
    public void deleteProductTest() {
        DeleteProductRequestDTO request = new DeleteProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertTrue(response.getResult());
    }


    @Test
    @Order(301)
    @SneakyThrows
    public void queryProductNotExistTest() {
        QueryProductRequestDTO request = new QueryProductRequestDTO();
        request.setRequestId(UUID.randomUUID().toString());
        Response<List<QueryProductResponseDTO>> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 200);
        Assertions.assertFalse(response.getResult().stream().anyMatch(e -> e.getProductId().equals(PRODUCT_ID)));
    }

    @Test
    @Order(302)
    @SneakyThrows
    public void deleteProductNotExistTest() {
        DeleteProductRequestDTO request = new DeleteProductRequestDTO();
        request.setProductId(PRODUCT_ID);
        request.setRequestId(UUID.randomUUID().toString());
        Response<Boolean> response = sendRequest(request);
        Assertions.assertNotNull(response);
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getCode(), 400);
        Assertions.assertEquals(response.getMessage(), "请求参数错误");
    }


    private Response<List<QueryProductResponseDTO>> sendRequest(QueryProductRequestDTO request) {
        final String url = httpLocalhost + "/queryProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<List<QueryProductResponseDTO>>>() {
        });
    }

    private Response<Boolean> sendRequest(AddProductRequestDTO request) {
        final String url = httpLocalhost + "/addProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }

    private Response<Boolean> sendRequest(DeleteProductRequestDTO request) {
        final String url = httpLocalhost + "/deleteProduct";
        String res = HttpUtil.post(url, request);
        return JSONObject.parseObject(res, new TypeReference<Response<Boolean>>() {
        });
    }


}
