package com.bestpay.media.platform.rc.test.common;

import com.github.phantomthief.failover.impl.PriorityFailoverManager;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
@SpringBootTest
public class LoadBalanceTest {
  public LoadBalanceTest() {
    //todo
  }

  public static void main(String[] args) {
    PriorityFailoverManager failoverManager = null;
    failoverManager.getFailover();
    double[] weights = {0.1, 0.2, 0.3, 0.4};
    double weightSum = weights[0] + weights[1] + weights[2] + weights[3];
    long timeout = 3000;
    long avgTimeCost = 500;
    double tps = weightSum * 1000 / avgTimeCost;
    int watingTaskCount = 10;
    double load = (watingTaskCount * avgTimeCost) / (weightSum  * timeout / avgTimeCost );
  }

}
