# 服务配置
server:
  # 监听端口
  port: 48080
# 模型配置
model:
  # 模型节点
  service:
    # 背景照节点
    background:
      # 背景照节点默认配置
      service-default-config:
        # 节点并发限制
        max-concurrency: 1
      # 节点信息
      nodes:
        # 背景照节点1
        - uri: http://***************:7001/inference
        # 背景照节点2
        - uri: http://***************:7001/inference
          config:
            max-concurrency: 1
# 特征向量配置
vector-engine:
  # 模式, DEFAULT: 默认模式, MULTI_WRITE: 多写模式
  mode: MULTI_WRITE
  # 特征向量节点组
  group:
    # 特征向量节点组1
    - username: elastic
      password: 1Lf*o6JG_D5y5ul*TApb
      # 是否忽略https证书
      ignoreCert: true
      # 该节点组类型, READ_WRITE: 读写, READ: 只读, WRITE: 只写
      type: READ_WRITE
      # 节点信息
      nodes:
        - host: https://***************
          port: 9200
    # 特征向量节点组2
    - username: elastic
      password: o2pgssA1mHGcLNaiCVJQ
      ignoreCert: true
      type: READ_WRITE
      nodes:
        - host: https://***************
          port: 9200
# redis配置
redis:
  # redisson配置
  config: |
    # 单节点配置
    singleServerConfig:
      # 连接空闲超时（毫秒）
      idleConnectionTimeout: 10000
      # 连接超时（毫秒）
      connectTimeout: 10000
      # 命令等待超时（毫秒）
      timeout: 3000
      # 命令失败重试次数
      retryAttempts: 3
      # 命令重试发送时间间隔（毫秒）
      retryInterval: 1500
      # 密码
      password: NbCbYsjNb@114514
      # 单个连接最大订阅数量
      subscriptionsPerConnection: 5
      # 客户端名称
      clientName: null
      # 单节点地址
      address: "redis://127.0.0.1:6379"
      # 订阅连接最小空闲连接数
      subscriptionConnectionMinimumIdleSize: 1
      # 订阅连接池大小
      subscriptionConnectionPoolSize: 50
      # 连接池最小空闲连接数
      connectionMinimumIdleSize: 32
      # 连接池大小
      connectionPoolSize: 64
      # 数据库编号
      database: 0
      # DNS监控间隔（毫秒）
      dnsMonitoringInterval: 5000
    # 线程池数量
    threads: 0
    # Netty线程池数量
    nettyThreads: 0
    # 编码
    codec: !<org.redisson.codec.JsonJacksonCodec> { }

