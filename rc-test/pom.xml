<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>rc-gateway</artifactId>
    <groupId>com.bestpay.media.platform</groupId>
    <version>0.5.4</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>rc-test</artifactId>
  <packaging>jar</packaging>
  <version>0.5.4</version>
  <dependencies>
    <dependency>
      <groupId>com.bestpay.media.platform</groupId>
      <artifactId>rc-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bestpay.media.platform</groupId>
      <artifactId>rc-gateway-background</artifactId>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <java.version>1.8</java.version>
  </properties>
</project>
