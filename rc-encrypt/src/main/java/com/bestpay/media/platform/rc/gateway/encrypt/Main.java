package com.bestpay.media.platform.rc.gateway.encrypt;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;
import java.util.UUID;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR> 2021/8/23
 */
public class Main {

  public static final String DEFAULT_SYMMETRIC_ENCRYPTION = "Blowfish";

  public static void main(String[] args) {
    if (args == null || args.length == 0 || Objects.equals(args[0], "-h") || Objects.equals(args[0],
        "-help")) {
      printHelp();
    } else if (Objects.equals(args[0], "-g")) {
      System.out.println(genRandomKey());
    } else if (Objects.equals(args[0], "-e")) {
      if (args.length != 3) {
        System.out.println("Wrong parameters!");
      } else {
        System.out.println(encrypt(args[1],args[2]));
      }
    } else if (Objects.equals(args[0], "-d")) {
      if (args.length != 3) {
        System.out.println("Wrong parameters!");
      } else {
        System.out.println(decrypt(args[1],args[2]));
      }
    } else {
      printHelp();
    }
  }


  public static String genRandomKey() {
    String random = UUID.randomUUID().toString();
    random = random.replaceAll("-", "");
    byte[] bytes = random.getBytes(StandardCharsets.UTF_8);
    int mod = bytes.length % 8;
    if (mod != 0) {
      bytes = Arrays.copyOf(bytes, bytes.length - mod);
    }
    return Base64.getEncoder().encodeToString(bytes);
  }

  public static String encrypt(String key, String origin) {
    try {
      SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(key),
          DEFAULT_SYMMETRIC_ENCRYPTION);
      Cipher cipher = Cipher.getInstance(DEFAULT_SYMMETRIC_ENCRYPTION);
      cipher.init(Cipher.ENCRYPT_MODE, keySpec);
      byte[] en = cipher.doFinal(origin.getBytes(StandardCharsets.UTF_8));
      return Base64.getEncoder().encodeToString(en);
    } catch (Exception e) {
      e.printStackTrace();
      return "加密出错!";
    }
  }

  public static String decrypt(String key, String encrypted) {
    try {
      SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(key),
          DEFAULT_SYMMETRIC_ENCRYPTION);
      Cipher cipher = Cipher.getInstance(DEFAULT_SYMMETRIC_ENCRYPTION);
      cipher.init(Cipher.DECRYPT_MODE, keySpec);
      byte[] de = cipher.doFinal(Base64.getDecoder().decode(encrypted));
      return new String(de, StandardCharsets.UTF_8);
    } catch (Exception e) {
      e.printStackTrace();
      return "解密出错!";
    }
  }

  private static void printHelp() {
    System.out.println("The encryption and decryption tool for Bestpay, by Duan Ran.");
    System.out.println("Usage: java -jar rc-encrypt.jar -g");
    System.out.println("       java -jar rc-encrypt.jar -e <key> <origin>");
    System.out.println("       java -jar rc-encrypt.jar -d <key> <encrypted>");
    System.out.println();
    System.out.println("        -g       Generate a random secret key");
    System.out.println("        -e       Encrypting with the specified secret key");
    System.out.println("        -d       Decrypting with the specified secret key");
  }
}
