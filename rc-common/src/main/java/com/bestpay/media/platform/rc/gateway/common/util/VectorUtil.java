package com.bestpay.media.platform.rc.gateway.common.util;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2022/1/5
 */
public class VectorUtil {

    private VectorUtil() {
    }

    public static List<Float> convertVector(float[] vectors) {
        List<Float> list = new ArrayList<>(vectors.length);
        for (float vector : vectors) {
            list.add(vector);
        }
        return list;
    }

    public static List<List<Float>> convertVectors(float[][] vectors) {
        List<List<Float>> list = new ArrayList<>(vectors.length);
        for (float[] vector : vectors) {
            list.add(convertVector(vector));
        }
        return list;
    }

    public static float[] convertVector(List<Float> vectors) {
        float[] array = new float[vectors.size()];
        for (int i = 0; i < vectors.size(); i++) {
            array[i] = vectors.get(i);
        }
        return array;
    }

    public static float[][] convertVectors(List<List<Float>> vectors) {
        float[][] array = new float[vectors.size()][];
        for (int i = 0; i < vectors.size(); i++) {
            array[i] = convertVector(vectors.get(i));
        }
        return array;
    }

    public static ByteBuffer convertByteBuffer(String str) {
        byte[] bytes = str.getBytes();
        int byteCount = bytes.length;
        ByteBuffer vector = ByteBuffer.allocate(byteCount);
        for (byte b : bytes) {
            vector.put(b);
        }
        return vector;
    }

    public static List<Float> normalization(List<Float> vector) {
        float sum = 0;
        for (Float f : vector) {
            sum += f * f;
        }
        sum = (float) Math.sqrt(sum);
        List<Float> normalized = new ArrayList<>(vector.size());
        for (Float f : vector) {
            normalized.add(f / sum);
        }
        return normalized;
    }

}
