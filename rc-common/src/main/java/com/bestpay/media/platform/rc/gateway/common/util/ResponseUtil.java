package com.bestpay.media.platform.rc.gateway.common.util;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayErrorType.SUCCESS;

import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.error.ErrorInfo;
import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import lombok.experimental.UtilityClass;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@UtilityClass
public class ResponseUtil {

    public static <T> Response<T> success(Response<T> response, T result, String help) {
        response.setCode(SUCCESS.getHttpStatusCode());
        response.setMessage(SUCCESS.getMessage());
        response.setResult(result);
        response.setSuccess(true);
        response.setHelp(help);
        return response;
    }

    public static <T> Response<T> success(T result, String help) {
        return success(new Response<>(), result, help);
    }

    public static <T> Response<T> success(T result) {
        return success(new Response<>(), result, null);
    }

    private static <T> Response<T> onErrorInfo(Response<T> response, ErrorInfo errorInfo) {
        response.setMessage(errorInfo.getMessage());
        response.setCode(errorInfo.getCode());
        response.setHelp(errorInfo.getHelp());
        response.setSuccess(false);
        return response;
    }

    public static <T> Response<T> onError(Response<T> response, GatewayError errorType,
        Object... args) {
        return onErrorInfo(response, ErrorInfo.of(errorType, args));
    }

    public static <T> Response<T> onError(GatewayError errorType, Object... args) {
        return onError(new Response<>(), errorType, args);
    }

    public static <T> Response<T> onException(Response<T> response, GateWayException e) {
        return onErrorInfo(response, e.getErrorInfo());
    }

    public static <T> Response<T> onException(GateWayException e) {
        return onException(new Response<>(), e);
    }


}
