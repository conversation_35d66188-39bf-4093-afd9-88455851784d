package com.bestpay.media.platform.rc.gateway.common.model;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Component
public class ModelServiceManager {

    private final ModelServiceLoader modelServiceLoader;
    private final Map<String, Class<? extends ModelService>> modelServiceClassMap;
    private final Map<Class<? extends ModelService>, ModelServiceRegistry<?>> modelServiceRegistryMap;

    @Autowired
    public ModelServiceManager(ModelServiceLoader modelServiceLoader) {
        this.modelServiceLoader = modelServiceLoader;
        modelServiceClassMap = new ConcurrentHashMap<>();
        modelServiceRegistryMap = new ConcurrentHashMap<>();
    }

    public <M extends ModelService> M registerModelService(String modelServiceName,
        Class<M> modelServiceClass, ModelServiceChecker<M> modelServiceChecker) {
        ModelServiceRegistry<M> modelServiceRegistry = modelServiceLoader.loadModelService(
            modelServiceName, modelServiceClass, modelServiceChecker);
        modelServiceClassMap.put(modelServiceName, modelServiceClass);
        modelServiceRegistryMap.put(modelServiceClass, modelServiceRegistry);
        return modelServiceRegistry.getModelService();
    }


    @SuppressWarnings("unchecked")
    public <M extends ModelService> ModelServiceRegistry<M> getModelServiceRegistry(Class<M> clazz) {
        return (ModelServiceRegistry<M>) modelServiceRegistryMap.get(clazz);
    }

    @SuppressWarnings("unchecked")
    public <M extends ModelService> ModelServiceRegistry<M> getModelServiceRegistry(
        String serviceName) {
        Class<M> modelServiceClass = (Class<M>) modelServiceClassMap.get(serviceName);
        return getModelServiceRegistry(modelServiceClass);
    }

    public <M extends ModelService> M getModelService(Class<M> clazz) {
        ModelServiceRegistry<M> modelServiceRegistry = getModelServiceRegistry(clazz);
        return modelServiceRegistry == null ? null : modelServiceRegistry.getModelService();
    }

    @SuppressWarnings("unchecked")
    public <M extends ModelService> M getModelService(String serviceName) {
        Class<M> modelServiceClass = (Class<M>) modelServiceClassMap.get(serviceName);
        return getModelService(modelServiceClass);
    }

    public Map<Class<? extends ModelService>, ModelServiceRegistry<?>> getAllModelServiceRegistry() {
        return ImmutableMap.copyOf(modelServiceRegistryMap);
    }

    public Map<String, Class<? extends ModelService>> getAllModelServiceClass() {
        return ImmutableMap.copyOf(modelServiceClassMap);
    }

}
