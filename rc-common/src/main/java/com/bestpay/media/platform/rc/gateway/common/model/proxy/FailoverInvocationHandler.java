package com.bestpay.media.platform.rc.gateway.common.model.proxy;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.TOO_MANY_REQUEST;

import com.alibaba.fastjson2.JSON;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.github.phantomthief.failover.impl.PriorityFailover;
import com.github.phantomthief.failover.impl.PriorityFailoverManager;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> 2022/4/11
 */
@Slf4j
@AllArgsConstructor
public class FailoverInvocationHandler<M extends ModelService> implements InvocationHandler {

    private final PriorityFailoverManager<M> failoverManager;

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        PriorityFailover<M> failover = failoverManager.getFailover();
        M modelService = failover.getOneAvailable();
        if (modelService == null) {
            throw new GateWayException(TOO_MANY_REQUEST);
        }
        try {
            Object result = method.invoke(modelService, args);
            failover.success(modelService);
            // log.info("failover invoke success: {}, current weight {}", modelService, failover.getResourceStatus(modelService).getCurrentWeight());
            return result;
        } catch (Throwable throwable) {
            failover.fail(modelService);
            // log.error("failover invoke fail: {}, current weight {}", modelService, failover.getResourceStatus(modelService).getCurrentWeight());
            throw throwable;
        }
    }
}
