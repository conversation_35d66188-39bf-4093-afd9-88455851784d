package com.bestpay.media.platform.rc.gateway.common.vector.service;

import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import java.util.List;
import java.util.Map;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
public interface VectorService<V extends Vector> {

    String insertVector(V vector);

    boolean deleteVectorById(String id);

    boolean isVectorExistById(String id);

    List<SearchResult<V>> searchVector(SearchParam param);

    List<V> queryVectorList(int page, int limit);

    Map<String, Map<String, Long>> queryAggregations(List<String> fieldNames, int limit);

    List<V> queryOldestVectors(int offset, int limit);

}
