package com.bestpay.media.platform.rc.gateway.common.model.factory.rpc;

import com.bestpay.media.platform.rc.gateway.common.config.HttpConfig;
import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.NodeConfig;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.component.AddAuthorizationHeaderRequestFilter;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import com.google.common.base.Strings;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/3/20
 */
@Slf4j
@Component
public class HttpModelServiceFactory {

    private final ResteasyClient client;

    @Autowired
    public HttpModelServiceFactory(HttpConfig httpConfig) {
        this.client = buildClient(httpConfig);
    }

    private static ResteasyClient buildClient(HttpConfig httpConfig) {
        ResteasyClientBuilderImpl clientBuilder = new ResteasyClientBuilderImpl();
        clientBuilder.connectTimeout(httpConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
            .readTimeout(httpConfig.getReadTimeout(), TimeUnit.MILLISECONDS)
            .connectionPoolSize(httpConfig.getConnectionPoolSize())
            .connectionTTL(httpConfig.getConnectionPoolTimeToLive(), TimeUnit.MILLISECONDS)
            .connectionCheckoutTimeout(httpConfig.getConnectionCheckoutTimeout(),
                TimeUnit.MILLISECONDS);
        //  since okhttp3.3.0, 3.3 version, this behavior changes to
        //  Don't recover if we encounter a read timeout after sending the request,
        //  but do recover if we encounter a timeout building a connection.
        if (!httpConfig.getEnableAutomaticRetries()) {
            clientBuilder.disableAutomaticRetries();
        }
        if (!httpConfig.getEnableTrustManager()) {
            clientBuilder.disableTrustManager();
        }
        return clientBuilder.build();
    }

    @SneakyThrows
    public <M extends ModelService> M getModelService(ModelServiceNodeInfoConfig<M> info) {
        Class<M> modelServiceClass = info.getModelServiceClass();
        String uri = info.getUri();
        NodeConfig nodeConfig = info.getConfig();
        return creatRestProxy(modelServiceClass, uri, nodeConfig);
    }

    /**
     * 创建jax rs代理类
     *
     * @param interfaceClass 接口类class
     * @param uri 基础地址
     * @param <I> 接口类
     * @throws URISyntaxException url不合法
     */
    public <I> I creatRestProxy(Class<I> interfaceClass, String uri, NodeConfig config)
        throws URISyntaxException {
        ResteasyWebTarget webTarget = client.target(new URI(uri));
        registerFilter(webTarget, config);
        return webTarget.proxy(interfaceClass);
    }

    private void registerFilter(ResteasyWebTarget webTarget, NodeConfig config) {
        if (config == null) {
            return;
        }
        String authorization = config.getAuthorization();
        if (!Strings.isNullOrEmpty(authorization)) {
            webTarget.register(new AddAuthorizationHeaderRequestFilter(authorization));
        }
    }
}
