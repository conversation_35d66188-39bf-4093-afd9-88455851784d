package com.bestpay.media.platform.rc.gateway.common.model.factory;

import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import com.bestpay.media.platform.rc.gateway.common.model.enums.ModelRpcType;
import com.bestpay.media.platform.rc.gateway.common.model.factory.rpc.HttpModelServiceFactory;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/3/19
 */
@Component
public class RpcModeServiceFactory {

    private final HttpModelServiceFactory httpModelServiceFactory;


    @Autowired
    public RpcModeServiceFactory(HttpModelServiceFactory httpModelServiceFactory) {
        this.httpModelServiceFactory = httpModelServiceFactory;
    }

    public <M extends ModelService> M getModelService(ModelServiceNodeInfoConfig<M> info) {
        String uri = info.getUri();
        ModelRpcType modelLocalType = determineModelRpcType(uri);
        if (modelLocalType == ModelRpcType.HTTP) {
            return httpModelServiceFactory.getModelService(info);
        }
        throw new ConfigInvalidException("model.service.?.nodes.uri", "不支持的模型uri:" + uri);
    }

    private static ModelRpcType determineModelRpcType(String uriStr) {
        try {
            URI uri = new URI(uriStr);
            return Arrays.stream(ModelRpcType.values())
                .filter(type -> Arrays.stream(type.getSchemes())
                    .filter(s -> s != null && !s.isEmpty())
                    .anyMatch(s -> s.equalsIgnoreCase(uri.getScheme())))
                .findFirst().orElse(null);
        } catch (URISyntaxException e) {
            return null;
        }
    }
}
