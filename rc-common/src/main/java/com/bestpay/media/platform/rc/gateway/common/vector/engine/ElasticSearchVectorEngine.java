package com.bestpay.media.platform.rc.gateway.common.vector.engine;

import static com.bestpay.media.platform.rc.gateway.common.vector.component.EngineConstant.CREATION_DATE_FIELD;
import static com.bestpay.media.platform.rc.gateway.common.vector.component.EngineConstant.VECTOR_FIELD;
import static com.bestpay.media.platform.rc.gateway.common.vector.component.EngineConstant.WILDCARD;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.KnnSearch.Builder;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.DeleteResponse;
import co.elastic.clients.elasticsearch.core.IndexResponse;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.SourceFilter;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.DeleteIndexResponse;
import co.elastic.clients.elasticsearch.indices.GetIndexResponse;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.endpoints.BooleanResponse;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.bestpay.media.platform.rc.gateway.common.vector.component.ElasticExceptionHandler;
import com.bestpay.media.platform.rc.gateway.common.vector.component.ElasticSearchConvertor;
import com.bestpay.media.platform.rc.gateway.common.vector.component.ElasticSearchTask;
import com.bestpay.media.platform.rc.gateway.common.vector.component.UnsafeSSLContextFactory;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.ElasticSearchConfig;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import com.google.common.base.Strings;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;


/**
 * ElasticSearch Vector SearchEngine The client is designed with RestHighLevelClient to support es 8.13
 * <p>
 * Must be tested completely before deployment with other es versions!
 *
 * <AUTHOR> 2023/5/4
 */
@Slf4j
public class ElasticSearchVectorEngine implements VectorEngine {

    private final ElasticSearchConvertor convertor;
    private final ElasticsearchTransport transport;
    private final ElasticsearchClient esClient;
    private final RestClient restClient;

    public ElasticSearchVectorEngine(ElasticSearchConfig config) {
        this.restClient = buildRestClient(config);
        JacksonJsonpMapper jsonpMapper = new JacksonJsonpMapper();
        this.transport = new RestClientTransport(restClient, jsonpMapper);
        this.convertor = new ElasticSearchConvertor(config, jsonpMapper);
        this.esClient = new ElasticsearchClient(transport);
    }

    private RestClient buildRestClient(ElasticSearchConfig config) {
        RestClientBuilder restClientBuilder = RestClient.builder(config.getHosts());
        restClientBuilder.setHttpClientConfigCallback(builder -> {
            if (!Strings.isNullOrEmpty(config.getUsername()) && !Strings.isNullOrEmpty(config.getPassword())) {
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(config.getUsername(), config.getPassword()));
                builder.setDefaultCredentialsProvider(credentialsProvider);
            }
            if (Boolean.TRUE.equals(config.getIgnoreCert())) {
                builder.setSSLContext(UnsafeSSLContextFactory.getUnsafeSSLContext());
            }
            return builder;
        });
        return restClientBuilder.build();
    }

    @Override
    public List<Collection> queryCollectionInfoList() {
        //TODO 添加查询分页
        return execute(() -> {
            GetIndexResponse response = esClient
                .indices()
                .get(r -> r
                    .index(convertor.toIndexName(WILDCARD))
                );
            Map<String, Collection> collections = convertor.toCollection(response);
            return collections.values().parallelStream()
                .peek(collection -> collection.setCount(countIndexDocuments(collection.getName())))
                .collect(Collectors.toList());
        });
    }

    @Override
    public Collection queryCollectionInfo(String collectionName) {
        return execute(() -> {
            GetIndexResponse response = esClient
                .indices()
                .get(r -> r
                    .index(convertor.toIndexName(collectionName))
                );
            Map<String, Collection> collections = convertor.toCollection(response);
            Collection collection = collections.get(collectionName);
            if (collection == null) {
                return null;
            }
            collection.setCount(countIndexDocuments(collectionName));
            return collection;
        });
    }

    @Override
    public boolean createCollection(String collectionName, CollectionParam param) {
        return execute(() -> {
            CreateIndexResponse response = esClient
                .indices()
                .create(r -> r
                    .index(convertor.toIndexName(collectionName))
                    .settings(convertor.toSetting(param))
                    .mappings(convertor.toMapping(param))
                );
            return !Strings.isNullOrEmpty(response.index());
        });
    }

    @Override
    public boolean isCollectionExist(String collectionName) {
        return execute(() -> {
            BooleanResponse response = esClient
                .indices()
                .exists(r -> r
                    .index(convertor.toIndexName(collectionName))
                );
            return response.value();
        });
    }

    @Override
    public boolean deleteCollection(String collectionName) {
        return execute(() -> {
            DeleteIndexResponse response = esClient
                .indices()
                .delete(r -> r
                    .index(convertor.toIndexName(collectionName))
                );
            return response.acknowledged();
        });
    }

    @Override
    public String insertVector(String collectionName, Vector vector) {
        return execute(() -> {
            IndexResponse response = esClient
                .index(r -> r
                    .index(convertor.toIndexName(collectionName))
                    .id(vector.getId())
                    .document(vector)
                );
            return response.id();
        });
    }

    @Override
    public boolean deleteVectorById(String collectionName, String id) {
        return execute(() -> {
            DeleteResponse response = esClient
                .delete(r -> r
                    .index(convertor.toIndexName(collectionName))
                    .id(id)
                );
            return id.equalsIgnoreCase(response.id());
        });
    }

    @Override
    public boolean isVectorExistById(String collectionName, String id) {
        return execute(() -> {
            BooleanResponse response = esClient
                .exists(r -> r
                    .index(convertor.toIndexName(collectionName))
                    .id(id)
                );
            return response.value();
        });
    }

    @Override
    public <V extends Vector> List<SearchResult<V>> searchVector(String collectionName, Class<V> vectorClass, SearchParam param) {
        return execute(() -> {
            Map<String, String> paramFilterMap = param.getFilter();
            SearchResponse<V> response = esClient
                .search(r -> r
                        .index(convertor.toIndexName(collectionName))
                        .knn(k -> {
                                Builder builder = k
                                    .field(VECTOR_FIELD)
                                    .queryVector(param.getVector())
                                    .numCandidates(param.getNumCandidates())
                                    .k(param.getTopK());
                                if (paramFilterMap != null && !paramFilterMap.isEmpty()) {
                                    builder.filter(f -> {
                                        paramFilterMap.forEach((field, value) ->
                                            f.term(t -> t.field(field).value(value))
                                        );
                                        return f;
                                    });
                                }
                                return builder;
                            }
                        ),
                    vectorClass);
            return response.hits() == null || response.hits().hits() == null || response.hits().hits().isEmpty()
                ? Collections.emptyList()
                : response.hits().hits().stream()
                    .map(convertor::toSearchResult)
                    .sorted((v1, v2) -> Double.compare(v2.getScore(), v1.getScore()))
                    .collect(Collectors.toList());
        });
    }

    @Override
    public <V extends Vector> List<V> queryVectorList(String collectionName, Class<V> vectorClass, int page, int limit) {
        return execute(() -> {
            SearchResponse<V> response = esClient
                .search(r -> r
                        .index(convertor.toIndexName(collectionName))
                        .source(s -> s
                            .filter(SourceFilter.of(f -> f
                                .excludes(VECTOR_FIELD))))
                        .from(page * limit)
                        .size(limit),
                    vectorClass);
            return response.hits() == null || response.hits().hits() == null || response.hits().hits().isEmpty()
                ? Collections.emptyList()
                : response.hits().hits().stream()
                    .map(convertor::toVector)
                    .collect(Collectors.toList());
        });
    }

    @Override
    public <V extends Vector> Map<String, Map<String, Long>> queryAggregations(String collectionName, Class<V> vectorClass,
        List<String> fieldNames, int limit) {
        return execute(() -> {
            SearchResponse<V> response = esClient
                .search(r -> r
                        .index(convertor.toIndexName(collectionName))
                        .source(s -> s
                            .fetch(false)
                        )
                        .aggregations(fieldNames.stream()
                            .collect(Collectors.toMap(
                                Function.identity(),
                                fieldName ->
                                    Aggregation.of(a -> a
                                        .terms(t -> t
                                            .field(fieldName)
                                            .size(limit)
                                        )))))
                        .size(limit),
                    vectorClass);
            return response.aggregations() == null || response.aggregations().isEmpty()
                ? Collections.emptyMap()
                : response.aggregations().entrySet().stream()
                    .collect(Collectors.toMap(
                        Entry::getKey,
                        e -> convertor.toAggregation(e.getValue()))
                    );
        });
    }

    @Override
    public <V extends Vector> List<V> queryOldestVectors(String collectionName, Class<V> vectorClass, int offset, int limit) {
        return execute(() -> {
            SearchResponse<V> response = esClient.search(r -> r
                    .index(convertor.toIndexName(collectionName))
                    .source(s -> s
                        .filter(SourceFilter.of(f -> f
                            .excludes(VECTOR_FIELD))))
                    .sort(s -> s
                        .field(f -> f
                            .field(CREATION_DATE_FIELD)
                            .order(SortOrder.Desc)))
                    .from(offset)
                    .size(limit),
                vectorClass);
            return response.hits() == null || response.hits().hits() == null || response.hits().hits().isEmpty()
                ? Collections.emptyList()
                : response.hits().hits().stream()
                    .map(convertor::toVector)
                    .collect(Collectors.toList());
        });
    }


    public long countIndexDocuments(String collectionName) {
        try {
            CountResponse response = esClient
                .count(r -> r
                    .index(convertor.toIndexName(collectionName))
                );
            return response.count();
        } catch (Exception e) {
            log.error("count index documents error", e);
            return -1;
        }
    }

    @SneakyThrows
    private <R> R execute(ElasticSearchTask<R> task) {
        try {
            return task.execute();
        } catch (Exception e) {
            throw ElasticExceptionHandler.handle(e);
        }
    }

    @PreDestroy
    public void close() {
        if (transport != null) {
            IOUtils.closeQuietly(transport);
        }
        if (restClient != null) {
            IOUtils.closeQuietly(restClient);
        }
    }

}
