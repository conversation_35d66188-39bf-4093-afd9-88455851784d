package com.bestpay.media.platform.rc.gateway.common.error.exception;

import com.bestpay.media.platform.rc.gateway.common.error.ErrorInfo;
import lombok.Getter;

/**
 * @Author: 段然
 * @Date: 2024/9/10
 */
@Getter
public abstract class ErrorInfoException extends RuntimeException {

    private final ErrorInfo errorInfo;

    protected ErrorInfoException(ErrorInfo errorInfo) {
        super(errorInfo.getExceptionMessage());
        this.errorInfo = errorInfo;
    }

    protected ErrorInfoException(ErrorInfo errorInfo, Throwable cause) {
        super(errorInfo.getExceptionMessage(), cause);
        this.errorInfo = errorInfo;
    }

}
