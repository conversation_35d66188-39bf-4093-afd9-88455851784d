package com.bestpay.media.platform.rc.gateway.common.controller;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import com.bestpay.media.platform.rc.gateway.common.component.VersionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2021/12/13
 */
@Slf4j
@RestController
@RequestMapping(produces = "application/json")
public class CommonController {

    private final VersionManager versionManager;

    public CommonController(VersionManager versionManager) {
        this.versionManager = versionManager;
    }

    @RequestMapping(value = "/version", method = {GET, POST})
    public String version() {
        return versionManager.getVersion();
    }

}
