package com.bestpay.media.platform.rc.gateway.common.global;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INTERNAL_SERVER_ERROR;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_ACCEPTABLE_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_SUPPORTED_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_HTTP_MESSAGE_NOT_READABLE_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_HTTP_REQUEST_METHOD_NOT_SUPPORTED_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_MISSING_REQUEST_VALUE_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INVALID_PARAM_MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION;

import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.ResponseAdapter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.servlet.ServletException;
import javax.ws.rs.ProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestValueException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR> 2022/3/16 这个类应当只负责对外部请求参数错误异常和未知异常的处理，对于已知异常，应当在对应的模块中进行捕获处理，如果是必须抛出给调用方的异常，应当定义Error枚举并包装到Exception中抛出
 */
@Slf4j
@Order(40)
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final Map<Class<? extends Throwable>, Function<? extends Throwable, Object>> exceptionHandlers;
    private final GlobalContext globalContext;

    public GlobalExceptionHandler(GlobalContext globalContext) {
        this.exceptionHandlers = new ConcurrentHashMap<>();
        this.globalContext = globalContext;
        initExceptionHandlers();
    }

    private void initExceptionHandlers() {
        initExceptionHandler(Exception.class, this::handleUnknownException);
        initExceptionHandler(Throwable.class, this::handleUnknownException);

        initExceptionHandler(Exception.class, this::handleUnknownException);
        initExceptionHandler(Throwable.class, this::handleUnknownException);

        initExceptionHandler(ExecutionException.class, this::handleNestedException);
        initExceptionHandler(ProcessingException.class, this::handleNestedException);
        initExceptionHandler(InvocationTargetException.class, this::handleNestedException);
        initExceptionHandler(UndeclaredThrowableException.class, this::handleNestedException);

        //todo add 404 to specialErrorCodeMap
        //todo add http timeout to specialErrorCodeMap
        initExceptionHandler(MissingRequestValueException.class, INVALID_PARAM_MISSING_REQUEST_VALUE_EXCEPTION);
        initExceptionHandler(HttpMessageNotReadableException.class, INVALID_PARAM_HTTP_MESSAGE_NOT_READABLE_EXCEPTION);
        initExceptionHandler(HttpMediaTypeNotSupportedException.class, INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_SUPPORTED_EXCEPTION
            , throwable -> ((HttpMediaTypeNotSupportedException) throwable).getContentType());
        initExceptionHandler(HttpMediaTypeNotAcceptableException.class, INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_ACCEPTABLE_EXCEPTION,
            throwable -> ((HttpMediaTypeNotAcceptableException) throwable).getSupportedMediaTypes());
        initExceptionHandler(HttpRequestMethodNotSupportedException.class, INVALID_PARAM_HTTP_REQUEST_METHOD_NOT_SUPPORTED_EXCEPTION,
            throwable -> ((HttpRequestMethodNotSupportedException) throwable).getMethod());
        initExceptionHandler(MissingServletRequestParameterException.class, INVALID_PARAM_MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION,
            throwable -> ((MissingServletRequestParameterException) throwable).getParameterName());
        initExceptionHandler(ServletException.class, INVALID_PARAM);

        initExceptionHandler(GateWayException.class, this::handleGateWayException);
        initExceptionHandler(MethodArgumentNotValidException.class, this::handleValidationExceptions);
    }

    @ExceptionHandler(Throwable.class)
    public Object handle(Throwable e) {
        try {
            return getHandler(e).apply(e);
        } catch (Throwable t) {
            log.error("handle exception error", t);
        }
        return handleUnknownException(e);
    }

    private Object handleUnknownException(Throwable e) {
        log.error("handle exception", e);
        return adaptError(INTERNAL_SERVER_ERROR);
    }

    private Object handleNestedException(Throwable e) {
        return e.getCause() != null ? handle(e.getCause()) : handleUnknownException(e);
    }

    private Object handleValidationExceptions(Throwable methodArgumentNotValidException) {
        List<FieldError> errors = ((MethodArgumentNotValidException) methodArgumentNotValidException)
            .getBindingResult().getFieldErrors();
        return adaptError(INVALID_PARAM, errors.isEmpty() ? null : errors.stream()
            .filter(fieldError -> fieldError.getDefaultMessage() != null)
            .sorted(Comparator.comparing(FieldError::getField)
                .thenComparing(FieldError::getDefaultMessage))
            .map(error -> String.format(error.getDefaultMessage(), error.getField()))
            .collect(Collectors.joining(", ")));
    }

    @SuppressWarnings("unchecked")
    private <T extends Throwable> Function<T, Object> getHandler(T e) {
        return (Function<T, Object>) getHandlerForClass(e.getClass());
    }

    @SuppressWarnings("unchecked")
    private <T extends Throwable> Function<T, Object> getHandlerForClass(Class<T> eClass) {
        Function<? extends Throwable, Object> function = exceptionHandlers.computeIfAbsent(eClass,
            c -> c.getSuperclass() == c ? this::handleUnknownException
                : getHandlerForClass((Class<? extends Throwable>) c.getSuperclass()));
        return (Function<T, Object>) function;
    }

    private Object handleGateWayException(Throwable serviceException) {
        log.error("handle gateway exception", serviceException);
        GateWayException gateWayException = (GateWayException) serviceException;
        return adaptGateWayException(gateWayException);
    }

    private Object adapt(Function<ResponseAdapter<Object>, Object> function) {
        ResponseAdapter<Object> adapter = globalContext.getResponseAdapter();
        return adapter == null ? null : function.apply(adapter);
    }

    private Object adaptError(GatewayError gatewayError, Object... args) {
        return adapt(adapter -> adapter.onError(gatewayError, args));
    }

    private Object adaptGateWayException(GateWayException e) {
        return adapt(adapter -> adapter.onException(e));
    }

    private void initExceptionHandler(Class<? extends Throwable> exceptionClass,
        Function<? extends Throwable, Object> handler) {
        exceptionHandlers.put(exceptionClass, handler);
    }

    private void initExceptionHandler(Class<? extends Throwable> exceptionClass, GatewayError error, Function<Object, Object> argFunction) {
        initExceptionHandler(exceptionClass, throwable -> adaptError(error, argFunction.apply(throwable)));
    }

    private void initExceptionHandler(Class<? extends Throwable> exceptionClass, GatewayError error) {
        initExceptionHandler(exceptionClass, throwable -> adaptError(error));
    }
}
