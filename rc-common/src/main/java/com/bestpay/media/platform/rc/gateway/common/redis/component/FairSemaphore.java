package com.bestpay.media.platform.rc.gateway.common.redis.component;

import java.util.concurrent.TimeUnit;
import org.redisson.api.RLock;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RPermitExpirableSemaphoreReactive;
import org.redisson.api.RSemaphore;

/**
 * @Author: 段然
 * @Date: 2024/3/26
 */
public class FairSemaphore {

    private final int leaseTime;
    private final RLock fairLock;
    private final RPermitExpirableSemaphore semaphore;

    public FairSemaphore(int leaseTime, RPermitExpirableSemaphore semaphore, RLock fairLock) {
        this.leaseTime = leaseTime;
        this.semaphore = semaphore;
        this.fairLock = fairLock;

    }

    public String acquire() throws InterruptedException {
        fairLock.lock(leaseTime, TimeUnit.MILLISECONDS);
        try {
            return semaphore.acquire(leaseTime, TimeUnit.MILLISECONDS);
        } finally {
            fairLock.unlock();
        }
    }

    public void release(String permitId) {
        semaphore.tryRelease(permitId);
    }

    public void trySetPermits(int permits) {
        semaphore.trySetPermits(permits);
    }

}
