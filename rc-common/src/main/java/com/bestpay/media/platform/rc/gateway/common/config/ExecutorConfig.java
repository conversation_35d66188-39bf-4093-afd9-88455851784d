package com.bestpay.media.platform.rc.gateway.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/15
 */
@Data
@Component
@ConfigurationProperties(prefix = "gateway.executor")
public class ExecutorConfig {

    private Integer maxPoolSize;

    private Integer corePoolSize;

    private Integer queueCapacity;

    private Integer keepAliveSeconds;

    private Boolean allowCoreThreadTimeOut;

    private String threadNamePrefix;

}

