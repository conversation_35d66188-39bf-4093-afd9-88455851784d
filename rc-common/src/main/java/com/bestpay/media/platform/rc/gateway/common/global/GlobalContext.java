package com.bestpay.media.platform.rc.gateway.common.global;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.RequestAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.ResponseAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.FastFail;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.global.log.LogConstant;
import com.bestpay.media.platform.rc.gateway.common.util.ClassUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.lang.reflect.Method;
import java.util.UUID;
import javassist.ClassPool;
import javassist.NotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

/**
 * <AUTHOR> 2022/3/16
 */
@Slf4j
@Component
public class GlobalContext {

    private final ApplicationContext applicationContext;
    private final LoadingCache<Method, String> methodLineNumberLoadingCache;
    private final LoadingCache<Class<?>, Object> springBeanLoadingCache;
    private final TransmittableThreadLocal<GlobalInfo> threadLocal;

    @Autowired
    public GlobalContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.threadLocal = new TransmittableThreadLocal<>();
        this.methodLineNumberLoadingCache = Caffeine.newBuilder()
            .build(this::getMethodLineNumber);
        this.springBeanLoadingCache = Caffeine.newBuilder()
            .build(this::getSpringBean);
    }

    private Object getSpringBean(Class<?> clazz) {
        return applicationContext.getBean(clazz);
    }

    public void initContext(HandlerMethod handlerMethod) {
        initGlobalInfo(handlerMethod);
        initThreadContext(handlerMethod);
    }

    @SuppressWarnings("unchecked")
    private void initGlobalInfo(HandlerMethod handlerMethod) {
        GlobalInfo globalInfo = threadLocal.get();
        if (globalInfo == null) {
            globalInfo = new GlobalInfo();
            threadLocal.set(globalInfo);
        }
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();
        Filter filterAnnotation = ClassUtil.getAnnotation(handlerMethod, Filter.class);
        FastFail fastFailAnnotation = ClassUtil.getAnnotation(handlerMethod, FastFail.class);
        if (filterAnnotation != null) {
            RequestAdapter<Object> requestAdapter = (RequestAdapter<Object>) springBeanLoadingCache.get(filterAnnotation.requestAdapter());
            ResponseAdapter<Object> responseAdapter = (ResponseAdapter<Object>) springBeanLoadingCache.get(
                filterAnnotation.responseAdapter());
            globalInfo.setRequestAdapter(requestAdapter);
            globalInfo.setResponseAdapter(responseAdapter);
        }
        globalInfo.setFilter(filterAnnotation);
        globalInfo.setFastFail(fastFailAnnotation);
        globalInfo.setRequestTime(System.currentTimeMillis());
        globalInfo.setElapsedTime(globalInfo.getRequestTime());
        globalInfo.setServiceClass(clazz);
        globalInfo.setMethod(method);
    }


    private void initThreadContext(HandlerMethod handlerMethod) {
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();
        String className = clazz.getName();
        String methodName = method.getName();
        String methodLine = methodLineNumberLoadingCache.get(method);
        ThreadContext.put(LogConstant.CLASS_NAME, className);
        ThreadContext.put(LogConstant.METHOD_NAME, methodName);
        ThreadContext.put(LogConstant.METHOD_LINE, methodLine);
        ThreadContext.put(LogConstant.TRACE_LOG_ID, UUID.randomUUID().toString());
        ThreadContext.put(LogConstant.ELAPSED_TIME, "");
    }

    private String getMethodLineNumber(Method method) {
        try {
            return String.valueOf(
                ClassPool.getDefault().get(method.getDeclaringClass().getCanonicalName())
                    .getDeclaredMethod(method.getName()).getMethodInfo().getLineNumber(0));
        } catch (NotFoundException ignored) {
        } catch (Exception e) {
            log.warn("failed to get line number of method {}.", method.getName(), e);
        }
        return "";
    }

    public long getRequestCostTime() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? -1 : System.currentTimeMillis() - globalInfo.getRequestTime();
    }

    public Class<?> getServiceClass() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? null : globalInfo.getServiceClass();
    }

    public Method getMethod() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? null : globalInfo.getMethod();
    }

    public void setElapsed() {
        GlobalInfo globalInfo = threadLocal.get();
        if (globalInfo != null) {
            globalInfo.setElapsedTime(System.currentTimeMillis());
        }
    }

    public long getElapsed() {
        GlobalInfo globalInfo = threadLocal.get();
        if (globalInfo == null) {
            return -1;
        }
        long currentTimeMillis = System.currentTimeMillis();
        long elapsed = currentTimeMillis - globalInfo.getElapsedTime();
        globalInfo.setElapsedTime(currentTimeMillis);
        return elapsed;
    }

    public void clear() {
        ThreadContext.put(LogConstant.CLASS_NAME, "");
        ThreadContext.put(LogConstant.METHOD_NAME, "");
        ThreadContext.put(LogConstant.METHOD_LINE, "");
        ThreadContext.put(LogConstant.TRACE_LOG_ID, "");
        ThreadContext.put(LogConstant.ELAPSED_TIME, "");
        threadLocal.remove();
    }

    public Filter getLog() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? null : globalInfo.getFilter();
    }

    public boolean getFastFail() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo != null && globalInfo.getFastFail() != null;
    }

    public RequestAdapter<Object> getRequestAdapter() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? null : globalInfo.getRequestAdapter();
    }

    public ResponseAdapter<Object> getResponseAdapter() {
        GlobalInfo globalInfo = threadLocal.get();
        return globalInfo == null ? null : globalInfo.getResponseAdapter();
    }


}
