package com.bestpay.media.platform.rc.gateway.common.model.proxy;

import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.redis.component.FairSemaphore;
import com.google.common.collect.Sets;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> 2022/6/20
 */
@AllArgsConstructor
public class ConcurrencyInvocationHandler<M extends ModelService> implements InvocationHandler {

    private static final Set<String> FILTER_METHOD_NAMES = Sets.newHashSet("toString", "hashCode", "equals");
    private static final Set<Method> FILTER_METHODS = getFilterMethods();

    private final FairSemaphore semaphore;

    private final M modelService;

    private final String uri;

    @Override
    public Object invoke(Object target, Method method, Object[] args) throws Throwable {
        if (FILTER_METHODS.contains(method)) {
            return method.invoke(uri, args);
        }
        String permitId = null;
        try {
            permitId = semaphore.acquire();
            return method.invoke(modelService, args);
        } finally {
            semaphore.release(permitId);
        }
    }

    private static Set<Method> getFilterMethods() {
        return Arrays.stream(Object.class.getMethods())
            .filter(method -> FILTER_METHOD_NAMES.contains(method.getName()))
            .collect(Collectors.toSet());
    }

}
