package com.bestpay.media.platform.rc.gateway.common.global;

import com.bestpay.media.platform.rc.gateway.common.global.adapter.RequestAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.ResponseAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.FastFail;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import java.lang.reflect.Method;
import lombok.Data;

/**
 * <AUTHOR> 2022/12/12
 */
@Data
public class GlobalInfo {

    private Filter filter;

    private FastFail fastFail;

    private Long requestTime;

    private Long elapsedTime;

    private RequestAdapter<Object> requestAdapter;

    private ResponseAdapter<Object> responseAdapter;

    private Class<?> serviceClass;

    private Method method;

}
