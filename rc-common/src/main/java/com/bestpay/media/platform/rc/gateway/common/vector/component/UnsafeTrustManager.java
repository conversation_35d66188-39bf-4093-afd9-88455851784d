package com.bestpay.media.platform.rc.gateway.common.vector.component;

import java.security.cert.X509Certificate;
import javax.net.ssl.X509TrustManager;

/**
 * 信任所有证书
 *
 * @Author: 段然
 * @Date: 2024/9/4
 */
public class UnsafeTrustManager implements X509TrustManager {

    @Override
    public void checkClientTrusted(X509Certificate[] chain, String authType) {
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType) {
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return new X509Certificate[0];
    }
}
