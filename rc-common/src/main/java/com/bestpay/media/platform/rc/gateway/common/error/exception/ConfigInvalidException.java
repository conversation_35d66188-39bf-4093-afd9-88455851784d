package com.bestpay.media.platform.rc.gateway.common.error.exception;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INTERNAL_SERVER_ERROR_INVALID_CONFIG;

import com.bestpay.media.platform.rc.gateway.common.error.ErrorInfo;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
public class ConfigInvalidException extends ErrorInfoException {

    public ConfigInvalidException(String configPath, String message) {
        super(ErrorInfo.of(INTERNAL_SERVER_ERROR_INVALID_CONFIG, configPath, message));
    }

    public ConfigInvalidException(String configPath, String message, Throwable cause) {
        super(ErrorInfo.of(INTERNAL_SERVER_ERROR_INVALID_CONFIG, configPath, message), cause);
    }
}
