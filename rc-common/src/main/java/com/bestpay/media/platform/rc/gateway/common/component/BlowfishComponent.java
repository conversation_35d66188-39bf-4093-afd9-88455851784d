package com.bestpay.media.platform.rc.gateway.common.component;

import com.bestpay.media.platform.rc.gateway.common.config.EncryptConfig;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.UUID;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/8/23
 */
@Slf4j
@Component
public class BlowfishComponent {

    private static final String ENCRYPTION_METHOD = "Blowfish";
    private final Cipher decryptCipher;
    private final Cipher encryptCipher;

    @Autowired
    @SneakyThrows
    public BlowfishComponent(EncryptConfig encryptConfig) {
        byte[] decodeKey = Base64.getDecoder().decode(encryptConfig.getBlowFishKey());
        SecretKeySpec keySpec = new SecretKeySpec(decodeKey, ENCRYPTION_METHOD);
        this.encryptCipher = Cipher.getInstance(ENCRYPTION_METHOD);
        this.decryptCipher = Cipher.getInstance(ENCRYPTION_METHOD);
        encryptCipher.init(Cipher.ENCRYPT_MODE, keySpec);
        decryptCipher.init(Cipher.DECRYPT_MODE, keySpec);
    }

    @SneakyThrows
    public static String generateRandomKey() {
        String random = UUID.randomUUID().toString().replaceAll("-", "");
        byte[] bytes = random.getBytes(StandardCharsets.UTF_8);
        int mod = bytes.length % 8;
        if (mod != 0) {
            bytes = Arrays.copyOf(bytes, bytes.length - mod);
        }
        return Base64.getEncoder().encodeToString(bytes);
    }

    @SneakyThrows
    public String encrypt(String origin) {
        byte[] encrypted;
        byte[] originBytes = origin.getBytes(StandardCharsets.UTF_8);
        synchronized (this.encryptCipher) {
            encrypted = encryptCipher.doFinal(originBytes);
        }
        return Base64.getEncoder().encodeToString(encrypted);
    }

    @SneakyThrows
    public String decrypt(String encrypted) {
        byte[] decrypted;
        byte[] encryptedBytes = Base64.getDecoder().decode(encrypted);
        synchronized (this.decryptCipher) {
            decrypted = decryptCipher.doFinal(encryptedBytes);
        }
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    public String tryDecrypt(String usage, String encrypted) {
        try {
            return decrypt(encrypted);
        } catch (Exception e) {
            log.info("配置 {} 无法解密，使用原始值", usage);
            return encrypted;
        }
    }

}
