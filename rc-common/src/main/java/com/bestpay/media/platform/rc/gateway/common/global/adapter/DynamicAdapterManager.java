package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import com.bestpay.media.platform.rc.gateway.common.global.GlobalContext;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/9
 */
@Slf4j
@Component
public class DynamicAdapterManager {

    private static final Map<Class<?>, Optional<Adapter<?>>> WHITE_LIST = initWhiteList();
    private final Map<Class<? extends Adapter<?>>, Map<Class<?>, Optional<Adapter<?>>>> adapterCache;
    private final ApplicationContext applicationContext;
    private final GlobalContext globalContext;
    private final Map<Method, Optional<Class<?>>> returnTypeCache;
    private final Set<Class<?>> warnedSet;

    @Autowired
    public DynamicAdapterManager(GlobalContext globalContext, ApplicationContext applicationContext) {
        this.globalContext = globalContext;
        this.applicationContext = applicationContext;
        this.adapterCache = new ConcurrentHashMap<>();
        this.returnTypeCache = new ConcurrentHashMap<>();
        this.warnedSet = Collections.synchronizedSet(new HashSet<>());
    }

    private static Map<Class<?>, Optional<Adapter<?>>> initWhiteList() {
        Optional<Adapter<?>> empty = Optional.empty();
        Map<Class<?>, Optional<Adapter<?>>> whiteList = new HashMap<>();
        whiteList.put(String.class, empty);
        whiteList.put(Integer.class, empty);
        whiteList.put(Long.class, empty);
        whiteList.put(Double.class, empty);
        whiteList.put(Float.class, empty);
        whiteList.put(Boolean.class, empty);
        whiteList.put(Character.class, empty);
        whiteList.put(Byte.class, empty);
        whiteList.put(Short.class, empty);
        whiteList.put(Void.class, empty);
        whiteList.put(Object.class, empty);
        return whiteList;
    }

    public <A extends Adapter<T>, T> A getAdapter(Class<A> adapterClass, T target) {
        Class<?> targetClass = target == null ? getReturnType()
            : target instanceof Class ? (Class<?>) target : target.getClass();
        A adapter = getCachedAdapter(adapterClass, targetClass);
        if (targetClass != null && adapter == null && warnedSet.add(targetClass)) {
            log.warn("No adapter found for class: {}", targetClass.getName());
        }
        return adapter;
    }

    private Class<?> getReturnType() {
        Method method = globalContext.getMethod();
        return method == null ? null : returnTypeCache.computeIfAbsent(method,
                m -> m.getReturnType() == void.class ? Optional.empty() : Optional.of(m.getReturnType()))
            .orElse(null);
    }

    private <A extends Adapter<?>> A getCachedAdapter(Class<A> adapterClass, Class<?> targetClass) {
        return adapterClass == null ? null
            : adapterCache.computeIfAbsent(adapterClass, k -> new ConcurrentHashMap<>(WHITE_LIST))
                .computeIfAbsent(targetClass,
                    k -> Optional.ofNullable(resolveAdapter(adapterClass, targetClass)))
                .map(adapterClass::cast).orElse(null);
    }

    private <A extends Adapter<?>> A resolveAdapter(Class<A> adapterClass, Class<?> targetClass) {
        ObjectProvider<A> adapterProvider = applicationContext.getBeanProvider(
            ResolvableType.forClassWithGenerics(adapterClass, targetClass));
        A adapter = adapterProvider.getIfAvailable();
        if (adapter != null) {
            return adapter;
        }
        Class<?> superClass = targetClass.getSuperclass();
        return superClass == null ? null : getCachedAdapter(adapterClass, superClass);
    }
}

