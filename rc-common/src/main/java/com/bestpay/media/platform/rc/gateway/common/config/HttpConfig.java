package com.bestpay.media.platform.rc.gateway.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "gateway.http")
public class HttpConfig {

    private Long readTimeout;
    private Long connectTimeout;
    private Integer connectionPoolSize;
    private Long connectionPoolTimeToLive;
    private Long connectionCheckoutTimeout;
    private Boolean enableAutomaticRetries;
    private Boolean enableTrustManager;
}
