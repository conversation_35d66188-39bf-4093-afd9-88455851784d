package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Slf4j
@Component
public class DynamicRequestAdapter implements RequestAdapter<Object> {

    private final DynamicAdapterManager dynamicAdapterManager;

    @Autowired
    public DynamicRequestAdapter(DynamicAdapterManager dynamicAdapterManager) {
        this.dynamicAdapterManager = dynamicAdapterManager;
    }

    public String getRequestId(Object request) {
        return execute(request, adapter -> adapter.getRequestId(request), null);
    }

    public Map<String, Object> getExtraInfo(Object request) {
        return execute(request, adapter -> adapter.getExtraInfo(request), null);
    }

    @SuppressWarnings("unchecked")
    private <R> R execute(Object request, Function<RequestAdapter<Object>, R> function, R defaultVal) {
        RequestAdapter<Object> adapter = dynamicAdapterManager.getAdapter(RequestAdapter.class, request);
        return adapter == null ? defaultVal : function.apply(adapter);
    }

    private void execute(Object Request, Consumer<RequestAdapter<Object>> consumer) {
        execute(Request, adapter -> {
            consumer.accept(adapter);
            return null;
        }, null);
    }


}
