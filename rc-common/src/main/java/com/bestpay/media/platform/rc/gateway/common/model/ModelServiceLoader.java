package com.bestpay.media.platform.rc.gateway.common.model;

import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig;
import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.FailoverConfig;
import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.ModelServiceConfig;
import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.ModelServiceNodeConfig;
import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.NodeConfig;
import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceCheckerWrapper;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import com.bestpay.media.platform.rc.gateway.common.model.proxy.ConcurrencyInvocationHandler;
import com.bestpay.media.platform.rc.gateway.common.model.proxy.FailoverInvocationHandler;
import com.bestpay.media.platform.rc.gateway.common.redis.RedisManager;
import com.bestpay.media.platform.rc.gateway.common.redis.component.FairSemaphore;
import com.bestpay.media.platform.rc.gateway.common.util.ClassUtil;
import com.github.phantomthief.failover.impl.PriorityFailover;
import com.github.phantomthief.failover.impl.PriorityFailoverBuilder.ResConfig;
import com.github.phantomthief.failover.impl.PriorityFailoverManager;
import com.github.phantomthief.failover.impl.RatioWeightFunction;
import java.lang.reflect.Proxy;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 模型服务加载器
 *
 * @Author: 段然
 * @Date: 2024/3/19
 */
@Slf4j
@Component
public class ModelServiceLoader {

    private final ModelConfig modelConfig;
    private final ModelServiceFactory modelServiceFactory;
    private final RedisManager redisManager;

    @Autowired
    public ModelServiceLoader(ModelServiceFactory modelServiceFactory,
        RedisManager redisManager, ModelConfig modelConfig) {
        this.modelServiceFactory = modelServiceFactory;
        this.redisManager = redisManager;
        this.modelConfig = modelConfig;
    }

    public <M extends ModelService> ModelServiceRegistry<M> loadModelService(String modelServiceName,
        Class<M> modelServiceClass, ModelServiceChecker<M> modelServiceChecker) {
        ModelServiceConfig modelServiceConfig = loadModelServiceConfig(modelServiceName);
        HashMap<M, ResConfig> nodeMap = modelServiceConfig.getNodes().stream()
            .peek(node -> node.setConfig(
                setDefaultConfig(
                    modelConfig::getGlobalDefaultConfig,
                    modelServiceConfig::getServiceDefaultConfig,
                    node::getConfig)))
            .map(node -> ModelServiceNodeInfoConfig.of(modelServiceClass, node))
            .collect(HashMap::new, (map, node) -> {
                M modelServiceProxy = createConcurrencyModelServiceProxy(node);
                ResConfig resConfig = createResConfig(node);
                map.put(modelServiceProxy, resConfig);
            }, HashMap::putAll);
        ModelServiceCheckerWrapper<M> modelServiceCheckerWrapper = new ModelServiceCheckerWrapper<>(
            modelServiceName, modelServiceClass, modelServiceChecker);
        PriorityFailoverManager<M> failoverManager = createFailoverManager(modelServiceCheckerWrapper);
        failoverManager.updateAll(nodeMap);
        M failoverModelServiceProxy = createFailoverModelServiceProxy(modelServiceClass, failoverManager);
        return new ModelServiceRegistry<>(failoverManager, modelServiceCheckerWrapper, failoverModelServiceProxy);
    }

    private ModelServiceConfig loadModelServiceConfig(String modelServiceName) {
        if (modelConfig == null) {
            throw new ConfigInvalidException("model.service", "没有配置模型服务");
        }
        Map<String, ModelServiceConfig> serviceConfigMap = modelConfig.getService();
        ModelServiceConfig modelServiceConfig = serviceConfigMap == null ?
            null : serviceConfigMap.get(modelServiceName);
        if (modelServiceConfig == null
            || modelServiceConfig.getNodes() == null
            || modelServiceConfig.getNodes().isEmpty()) {
            throw new ConfigInvalidException("model.service." + modelServiceName, "服务模型" + modelServiceName + "没有配置节点");
        }
        return modelServiceConfig;
    }

    private NodeConfig setDefaultConfig(Supplier<NodeConfig> globalConfig,
        Supplier<NodeConfig> modelConfig, Supplier<NodeConfig> nodeConfig) {
        NodeConfig global = globalConfig.get();
        NodeConfig model = modelConfig.get();
        NodeConfig node = nodeConfig.get();
        if (node == null) {
            node = new NodeConfig();
        }
        return node.setIfAbsent(model).setIfAbsent(global);
    }

    private ResConfig createResConfig(ModelServiceNodeConfig nodeConfig) {
        int priority = nodeConfig.getConfig().getPriority();
        double maxWeight = nodeConfig.getConfig().getMaxWeight();
        double minWeight = nodeConfig.getConfig().getMinWeight();
        double initWeight = nodeConfig.getConfig().getInitWeight();
        return new ResConfig(maxWeight, minWeight, priority, initWeight);
    }

    @SuppressWarnings("unchecked")
    private <M extends ModelService> M createConcurrencyModelServiceProxy(
        ModelServiceNodeInfoConfig<M> config) {
        String uri = config.getUri();
        Class<M> modelServiceClass = config.getModelServiceClass();
        M modelService = modelServiceFactory.getModelService(config);
        FairSemaphore semaphore = redisManager.getFairSemaphore(uri);
        semaphore.trySetPermits(config.getConfig().getMaxConcurrency());
        return (M) Proxy.newProxyInstance(modelServiceClass.getClassLoader(),
            ClassUtil.getInterfaces(modelServiceClass),
            new ConcurrencyInvocationHandler<>(semaphore, modelService, uri));
    }

    @SuppressWarnings("unchecked")
    private synchronized <M extends ModelService> M createFailoverModelServiceProxy(
        Class<M> modelServiceClass, PriorityFailoverManager<M> failoverManager) {
        return (M) Proxy.newProxyInstance(modelServiceClass.getClassLoader(),
            ClassUtil.getInterfaces(modelServiceClass),
            new FailoverInvocationHandler<>(failoverManager));
    }

    private <M extends ModelService> PriorityFailoverManager<M> createFailoverManager(
        ModelServiceChecker<M> modelServiceChecker) {
        FailoverConfig failover = modelConfig.getFailover();
        return PriorityFailover.<M>newBuilder()
            .concurrencyControl(failover.getEnableConcurrencyControl())
            .startCheckTaskImmediately(failover.getEnableCheckTaskImmediately())
            .checkDuration(Duration.ofMillis(failover.getCheckTaskInterval()))
            .checker(modelServiceChecker::check)
            .weightFunction(
                new RatioWeightFunction<>(
                    failover.getFailKeepRateOfCurrentWeight(),
                    failover.getSuccessIncreaseRateOfMaxWeight(),
                    failover.getRecoverThreshold(),
                    failover.getDownThreshold()))
            .buildManager();
    }

}
