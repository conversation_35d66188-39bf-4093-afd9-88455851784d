package com.bestpay.media.platform.rc.gateway.common.global.log;


import org.apache.logging.log4j.Marker;
import org.apache.logging.log4j.MarkerManager;

/**
 * <AUTHOR> 2019/9/27 0027
 */
public class LogMarker {

    public static final String PARAMETER = "PARAMETER";
    public static final String RESPONSE = "RESPONSE";
    public static final Marker PARAMETER_LOG_MARKER = MarkerManager.getMarker(PARAMETER);
    public static final Marker RESPONSE_LOG_MARKER = MarkerManager.getMarker(RESPONSE);

    private LogMarker() {
    }

}
