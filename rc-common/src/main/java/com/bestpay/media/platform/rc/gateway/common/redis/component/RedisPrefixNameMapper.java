package com.bestpay.media.platform.rc.gateway.common.redis.component;

import org.redisson.api.NameMapper;

/**
 * @Author: 段然
 * @Date: 2024/3/30
 */
public class RedisPrefixNameMapper implements NameMapper {

    private final String prefix;

    public RedisPrefixNameMapper(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public String map(String name) {
        return prefix == null ? name : prefix + name;
    }

    @Override
    public String unmap(String name) {
        return prefix == null ? name : name.substring(prefix.length());
    }

}

