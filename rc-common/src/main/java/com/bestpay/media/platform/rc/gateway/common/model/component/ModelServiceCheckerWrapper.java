package com.bestpay.media.platform.rc.gateway.common.model.component;

import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceChecker;
import com.google.common.collect.Sets;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import javax.ws.rs.ProcessingException;
import lombok.extern.slf4j.Slf4j;

/**
 * monitor model service health status and version
 */
@Slf4j
public class ModelServiceCheckerWrapper<M extends ModelService> implements ModelServiceChecker<M> {

    private static final Set<Class<?>> NESTED_EXCEPTION_CLASSES = initNestedExceptionClasses();
    private static final String VERSION_UNKNOWN = "unknown";
    private static final String STATUS_FAILED = "Failed";
    private static final String STATUS_OK = "OK";
    private final Map<M, String> versionMap;
    private final Map<M, Boolean> healthMap;
    private final ModelServiceChecker<M> modelServiceChecker;
    private final Class<M> modelServiceClass;
    private final String modelServiceName;

    public ModelServiceCheckerWrapper(String modelServiceName,
        Class<M> modelServiceClass, ModelServiceChecker<M> modelServiceChecker) {
        this.modelServiceChecker = modelServiceChecker;
        this.modelServiceClass = modelServiceClass;
        this.modelServiceName = modelServiceName;
        this.versionMap = new ConcurrentHashMap<>();
        this.healthMap = new ConcurrentHashMap<>();
    }

    @Override
    public boolean check(M modelService) {
        Boolean isHealth;
        String result;
        try {
            isHealth = modelServiceChecker.check(modelServiceClass.cast(modelService));
            result = isHealth ? STATUS_OK : STATUS_FAILED;
        } catch (Throwable e) {
            Throwable unwrappedException = unwrapNestedException(e);
            isHealth = false;
            result = STATUS_FAILED + ' ' + unwrappedException.getMessage();
        }
        Boolean previousStatus = healthMap.put(modelService, isHealth);
        if (!isHealth.equals(previousStatus)) {
            log.info("模型服务 {} 节点 {} 健康检查结果: {}", modelServiceName, modelService, result);
        }
        return isHealth;
    }

    @Override
    public String version(M modelService) {
        String version = VERSION_UNKNOWN;
        try {
            version = modelServiceChecker.version(modelServiceClass.cast(modelService));
        } catch (Throwable e) {
            Throwable unwrappedException = unwrapNestedException(e);
            log.info("模型服务 {} 节点 {} 版本检查失败: {}", modelServiceName, modelService, unwrappedException.getMessage());
        }
        String previousVersion = versionMap.put(modelService, version);
        if (previousVersion == null) {
            log.info("模型服务 {} 节点 {} 版本: {}",
                modelServiceName, modelService, version);
        } else if (!version.equals(previousVersion)) {
            log.info("模型服务 {} 节点 {} 版本更新: {} -> {}",
                modelServiceName, modelService, previousVersion, version);
        }
        return version;
    }

    private static Throwable unwrapNestedException(Throwable e) {
        if (NESTED_EXCEPTION_CLASSES.contains(e.getClass()) && e.getCause() != null) {
            return unwrapNestedException(e.getCause());
        }
        return e;
    }

    private static Set<Class<?>> initNestedExceptionClasses() {
        Set<Class<?>> nestedExceptionClasses = Sets.newHashSet();
        nestedExceptionClasses.add(UndeclaredThrowableException.class);
        nestedExceptionClasses.add(InvocationTargetException.class);
        nestedExceptionClasses.add(ProcessingException.class);
        nestedExceptionClasses.add(ExecutionException.class);
        return nestedExceptionClasses;
    }
}
