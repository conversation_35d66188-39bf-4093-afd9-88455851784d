package com.bestpay.media.platform.rc.gateway.common.model.component;

import java.io.IOException;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerRequestFilter;

/**
 * Add Authorization Header for 先觉
 *
 * @Author: 段然
 * @Date: 2024/3/22
 */
public class AddAuthorizationHeaderRequestFilter implements ContainerRequestFilter {

    private static final String AUTHORIZATION = "Authorization";
    private final String authorization;

    public AddAuthorizationHeaderRequestFilter(String authorizationMap) {
        this.authorization = authorizationMap;
    }

    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        requestContext.getHeaders().add(AUTHORIZATION, authorization);
    }
}
