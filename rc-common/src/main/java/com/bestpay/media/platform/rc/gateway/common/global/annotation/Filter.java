package com.bestpay.media.platform.rc.gateway.common.global.annotation;

import com.bestpay.media.platform.rc.gateway.common.global.adapter.DynamicRequestAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.DynamicResponseAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.RequestAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.ResponseAdapter;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> 2018/12/12 0007
 */

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface Filter {

    boolean value() default true;

    Class<? extends RequestAdapter<?>> requestAdapter() default DynamicRequestAdapter.class;

    Class<? extends ResponseAdapter<?>> responseAdapter() default DynamicResponseAdapter.class;

}
