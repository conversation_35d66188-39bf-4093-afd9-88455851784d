package com.bestpay.media.platform.rc.gateway.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "gateway.load-balance")
public class LoadBalanceConfig {

    // 平均接口耗时
    private Long avgTimeCost;
    // 启用快速失败
    private Boolean enableFastFailOnHighLoad;
    // 期望响应超时时间
    private Long expectedTimeout;
    // 系统高负载阈值
    private Double highLoadThreshold;
    // 系统低负载阈值
    private Double lowLoadThreshold;

}
