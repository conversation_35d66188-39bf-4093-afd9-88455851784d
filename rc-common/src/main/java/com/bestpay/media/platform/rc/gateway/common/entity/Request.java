package com.bestpay.media.platform.rc.gateway.common.entity;

import java.io.Serializable;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class Request implements Serializable {

    @Size(max = 128)
    private String requestId;

}
