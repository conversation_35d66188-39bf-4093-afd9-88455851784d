package com.bestpay.media.platform.rc.gateway.common.error;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.http.HttpStatus;

/**
 * @Author: 段然
 * @Date: 2024/3/26
 */
@Getter
@AllArgsConstructor
public enum GatewayErrorType {

    SUCCESS(HttpStatus.SC_OK, "请求成功", ""),

    INVALID_PARAM(HttpStatus.SC_BAD_REQUEST, "请求参数错误", "{}"),

    NOT_FOUND(HttpStatus.SC_NOT_FOUND, "接口不存在", "请参照接口文档检测请求路径{}是否正确"),

    NOT_ALLOWED(HttpStatus.SC_METHOD_NOT_ALLOWED, "请求方法错误", "请参照接口文档检测请求方法{}是否正确."),

    BAD_GATEWAY(HttpStatus.SC_BAD_GATEWAY, "调用外部服务错误", "请联系技术人员"),

    GATEWAY_TIMEOUT(HttpStatus.SC_GATEWAY_TIMEOUT, "调用外部服务超时", "请联系技术人员"),

    TOO_MANY_REQUEST(HttpStatus.SC_TOO_MANY_REQUESTS, "服务繁忙", "请稍后再试"),

    INTERNAL_SERVER_ERROR(HttpStatus.SC_INTERNAL_SERVER_ERROR, "服务内部错误", "请联系技术人员"),
    ;

    private final int httpStatusCode;
    private final String message;
    private final String defaultHelp;

}
