package com.bestpay.media.platform.rc.gateway.common.config;

import com.bestpay.media.platform.rc.gateway.common.component.BlowfishComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.SneakyThrows;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@Configuration
@EnableTransactionManagement
@ConditionalOnBean(DatabaseConfig.class)
@MapperScan(basePackages = {"com.bestpay.media.platform.**.mapper"})
public class DatabaseBeanConfig {

    private final BlowfishComponent blowfishComponent;

    @Autowired
    public DatabaseBeanConfig(BlowfishComponent blowfishComponent) {
        this.blowfishComponent = blowfishComponent;
    }

    @Bean
    public HikariDataSource hikariDataSource(DatabaseConfig databaseConfig) {
        HikariDataSource hikariDataSource = new HikariDataSource();
        hikariDataSource.setDriverClassName(databaseConfig.getDriverClassName());
        hikariDataSource.setMaximumPoolSize(databaseConfig.getMaxPoolSize());
        hikariDataSource.setJdbcUrl(databaseConfig.getUrl());
        hikariDataSource.setUsername(blowfishComponent.tryDecrypt("database.username", databaseConfig.getUserName()));
        hikariDataSource.setPassword(blowfishComponent.tryDecrypt("database.password", databaseConfig.getPassword()));
        hikariDataSource.setPoolName(databaseConfig.getPoolName());
        return hikariDataSource;
    }

    @Bean
    @SneakyThrows
    public SqlSessionFactory sqlSessionFactory(HikariDataSource hikariDataSource) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:/mapper/*.xml");
        Resource resource = resolver.getResource("classpath:/mybatis.xml");
        sqlSessionFactoryBean.setMapperLocations(resources);
        sqlSessionFactoryBean.setConfigLocation(resource);
        sqlSessionFactoryBean.setDataSource(hikariDataSource);
        return sqlSessionFactoryBean.getObject();
    }


}
