package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Slf4j
@Component
public class DynamicResponseAdapter implements ResponseAdapter<Object> {

    private final DynamicAdapterManager dynamicAdapterManager;

    @Autowired
    public DynamicResponseAdapter(DynamicAdapterManager dynamicAdapterManager) {
        this.dynamicAdapterManager = dynamicAdapterManager;
    }

    @Override
    public int getCode(Object response) {
        return execute(response, adapter -> adapter.getCode(response), 200);
    }

    @Override
    public boolean getSuccess(Object response) {
        return execute(response, adapter -> adapter.getSuccess(response), true);
    }

    @Override
    public void setElapsedTime(Object response, long elapsedTime) {
        execute(response, adapter -> adapter.setElapsedTime(response, elapsedTime));
    }

    @Override
    public Object onError(GatewayError gatewayError, Object... args) {
        return execute(null, adapter -> adapter.onError(gatewayError, args), null);
    }

    @Override
    public Object onException(GateWayException e) {
        return execute(null, adapter -> adapter.onException(e), null);
    }

    @SuppressWarnings("unchecked")
    private <R> R execute(Object response, Function<ResponseAdapter<Object>, R> function, R defaultVal) {
        ResponseAdapter<Object> adapter = dynamicAdapterManager.getAdapter(ResponseAdapter.class, response);
        return adapter == null ? defaultVal : function.apply(adapter);
    }

    private void execute(Object response, Consumer<ResponseAdapter<Object>> consumer) {
        execute(response, adapter -> {
            consumer.accept(adapter);
            return null;
        }, null);
    }

}
