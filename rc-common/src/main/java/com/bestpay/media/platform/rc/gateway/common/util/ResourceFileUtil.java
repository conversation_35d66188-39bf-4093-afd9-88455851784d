package com.bestpay.media.platform.rc.gateway.common.util;


import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.apache.commons.io.IOUtils;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@UtilityClass
public class ResourceFileUtil {

    @SneakyThrows
    public static String readString(String path) {
        try (InputStream inputStream = ResourceFileUtil.class.getResourceAsStream(path)) {
            return IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8);
        }
    }

    @SneakyThrows
    public static byte[] readBytes(String path) {
        try (InputStream inputStream = ResourceFileUtil.class.getResourceAsStream(path)) {
            return IOUtils.toByteArray(Objects.requireNonNull(inputStream));
        }
    }
}
