package com.bestpay.media.platform.rc.gateway.common.model;

import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import com.bestpay.media.platform.rc.gateway.common.model.enums.ModelLocalType;
import com.bestpay.media.platform.rc.gateway.common.model.enums.ModelRpcType;
import com.bestpay.media.platform.rc.gateway.common.model.factory.LocalModeServiceFactory;
import com.bestpay.media.platform.rc.gateway.common.model.factory.RpcModeServiceFactory;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/3/19
 */
@Component
public class ModelServiceFactory {

    private final LocalModeServiceFactory localModeServiceFactory;
    private final RpcModeServiceFactory rpcModeServiceFactory;

    public ModelServiceFactory(
        RpcModeServiceFactory rpcModeServiceFactory,
        LocalModeServiceFactory localModeServiceFactory) {
        this.rpcModeServiceFactory = rpcModeServiceFactory;
        this.localModeServiceFactory = localModeServiceFactory;
    }

    private static boolean isLocalModel(String path) {
        try {
            URI uri = new URI(path);
            String fileExtension = FilenameUtils.getExtension(path);
            return Arrays.stream(ModelLocalType.values())
                .flatMap(type -> Arrays.stream(type.getSchemes()))
                .filter(s -> s != null && !s.isEmpty())
                .anyMatch(s -> s.equalsIgnoreCase(uri.getScheme()))
                && Arrays.stream(ModelLocalType.values())
                .flatMap(type -> Arrays.stream(type.getExtension()))
                .filter(s -> s != null && !s.isEmpty())
                .anyMatch(s -> s.equalsIgnoreCase(fileExtension));
        } catch (URISyntaxException e) {
            return false;
        }
    }

    private static boolean isRpcModel(String uriStr) {
        try {
            URI uri = new URI(uriStr);
            return Arrays.stream(ModelRpcType.values())
                .flatMap(type -> Arrays.stream(type.getSchemes()))
                .filter(s -> s != null && !s.isEmpty())
                .anyMatch(s -> s.equalsIgnoreCase(uri.getScheme()));
        } catch (URISyntaxException e) {
            return false;
        }
    }

    public <M extends ModelService> M getModelService(ModelServiceNodeInfoConfig<M> info) {
        String uri = info.getUri();
        if (isRpcModel(uri)) {
            return rpcModeServiceFactory.getModelService(info);
        }
        if (isLocalModel(uri)) {
            return localModeServiceFactory.getModelService(info);
        }
        throw new ConfigInvalidException("model.service.?.nodes.uri", "不支持的模型路径: " + uri);
    }
}
