package com.bestpay.media.platform.rc.gateway.common.global;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Slf4j
@Order(10)
@Component
public class GlobalHandlerInterceptor implements HandlerInterceptor, WebMvcConfigurer {

    private final GlobalContext globalContext;

    @Autowired
    public GlobalHandlerInterceptor(GlobalContext globalContext) {
        this.globalContext = globalContext;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            globalContext.initContext((HandlerMethod) handler);
        } else {
            globalContext.clear();
        }
        return true;
    }

}
