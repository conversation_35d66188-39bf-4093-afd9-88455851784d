package com.bestpay.media.platform.rc.gateway.common.vector.component;

import static com.bestpay.media.platform.rc.gateway.common.vector.component.EngineConstant.META_FIELD;

import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.Buckets;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.GetIndexResponse;
import co.elastic.clients.elasticsearch.indices.IndexSettings;
import co.elastic.clients.elasticsearch.indices.IndexState;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.ElasticSearchConfig;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import com.google.common.base.Strings;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
@AllArgsConstructor
public class ElasticSearchConvertor {

    private final ElasticSearchConfig config;

    private final JacksonJsonpMapper mapper;

    public String toCollectionName(String indexName) {
        return Strings.isNullOrEmpty(config.getPrefix()) || !indexName.startsWith(config.getPrefix()) ? indexName
            : indexName.substring(config.getPrefix().length());
    }

    public String toCollectionMeta(IndexState index) {
        if (index == null || index.mappings() == null) {
            return null;
        }
        String mappings = JsonData.of(index.mappings()).toJson(mapper).toString();
        JSONObject mappingsJson = JSONObject.parseObject(mappings);
        return mappingsJson.containsKey(META_FIELD) ? mappingsJson.getJSONObject(META_FIELD).toJSONString() : "{}";
    }

    public String toIndexName(String collectionName) {
        return Strings.isNullOrEmpty(config.getPrefix()) ? collectionName : config.getPrefix() + collectionName;
    }

    public Map<String, Collection> toCollection(GetIndexResponse getIndexResponse) {
        return getIndexResponse == null || getIndexResponse.result() == null || getIndexResponse.result().isEmpty() ?
            Collections.emptyMap() : getIndexResponse.result().entrySet().stream()
            .collect(Collectors.toMap(
                e -> toCollectionName(e.getKey()),
                e -> {
                    Collection collection = new Collection();
                    collection.setName(toCollectionName(e.getKey()));
                    collection.setMeta(toCollectionMeta(e.getValue()));
                    return collection;
                }));
    }

    public <V extends Vector> SearchResult<V> toSearchResult(Hit<V> hit) {
        return SearchResult.<V>builder()
            .score(hit.score())
            .vector(hit.source())
            .build();
    }

    public <V extends Vector> V toVector(Hit<V> hit) {
        return hit.source();
    }

    public IndexSettings toSetting(CollectionParam collectionParam) {
        return JsonData.fromJson(collectionParam.getSetting()).to(IndexSettings.class, mapper);
    }

    public TypeMapping toMapping(CollectionParam collectionParam) {
        JSONObject mapping = JSON.parseObject(collectionParam.getMapping());
        JSONObject meta = JSON.parseObject(collectionParam.getMeta());
        if (meta != null) {
            if (mapping.containsKey(META_FIELD)) {
                mapping.getJSONObject(META_FIELD).putAll(meta);
            } else {
                mapping.put(META_FIELD, meta);
            }
        }
        return JsonData.fromJson(mapping.toJSONString()).to(TypeMapping.class, mapper);
    }

    public Map<String, Long> toAggregation(Aggregate aggregate) {
        if (aggregate != null) {
            StringTermsAggregate stringTermsAggregate = aggregate.sterms();
            if (stringTermsAggregate != null) {
                Buckets<StringTermsBucket> buckets = stringTermsAggregate.buckets();
                if (buckets != null) {
                    List<StringTermsBucket> array = buckets.array();
                    if (array != null && !array.isEmpty()) {
                        return array.stream().collect(Collectors.toMap(b -> b.key().stringValue(), StringTermsBucket::docCount));
                    }
                }
            }
        }
        return Collections.emptyMap();
    }
}
