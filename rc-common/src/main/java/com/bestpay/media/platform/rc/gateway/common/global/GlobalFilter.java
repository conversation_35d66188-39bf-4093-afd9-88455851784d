package com.bestpay.media.platform.rc.gateway.common.global;

import com.alibaba.fastjson2.JSON;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.RequestAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.adapter.ResponseAdapter;
import com.bestpay.media.platform.rc.gateway.common.global.annotation.Filter;
import com.bestpay.media.platform.rc.gateway.common.global.log.LogConstant;
import com.bestpay.media.platform.rc.gateway.common.global.log.LogFormat;
import com.bestpay.media.platform.rc.gateway.common.global.log.LogMarker;
import com.google.common.base.Strings;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2018/12/10 0010
 */
@Log4j2
@Component
public class GlobalFilter {

    private final GlobalContext globalContext;

    @Autowired
    public GlobalFilter(GlobalContext globalContext) {
        this.globalContext = globalContext;
    }

    private boolean checkEnable() {
        Filter contextLog = globalContext.getLog();
        return contextLog != null && contextLog.value();
    }

    public void beforeRequest(Object request) {
        if (checkEnable()) {
            setRequestContext(request);
            log.info(LogMarker.PARAMETER_LOG_MARKER, LogFormat.parameterFormat(), JSON.toJSONString(request));
        }
    }

    @SneakyThrows
    @SuppressWarnings("unchecked")
    private void setRequestContext(Object request) {
        RequestAdapter<Object> adapter = globalContext.getRequestAdapter();
        String traceId = format(adapter.getRequestId(request));
        String extraInfo = format(adapter.getExtraInfo(request));
        if (!Strings.isNullOrEmpty(traceId)) {
            ThreadContext.put(LogConstant.TRACE_LOG_ID, traceId);
        }
        ThreadContext.put(LogConstant.EXTRA_LOG_INFO, extraInfo);
        ThreadContext.put(LogConstant.ELAPSED_TIME, "");
    }

    public HttpStatus beforeResponse(Object response) {
        if (checkEnable()) {
            setResponseContext(response);
            log.info(LogMarker.RESPONSE_LOG_MARKER, LogFormat.responseFormat(),  JSON.toJSONString(response));
            return getHttpStatus(response);
        }
        return null;
    }

    public HttpStatus getHttpStatus(Object response) {
        ResponseAdapter<Object> adapter = globalContext.getResponseAdapter();
        return parseHttpStatus(adapter.getCode(response));
    }

    private HttpStatus parseHttpStatus(Integer code) {
        try {
            if (code != null) {
                return HttpStatus.valueOf(code);
            }
        } catch (IllegalArgumentException ignored) {
        }
        return null;
    }

    @SneakyThrows
    @SuppressWarnings("unchecked")
    private void setResponseContext(Object response) {
        ResponseAdapter<Object> adapter = globalContext.getResponseAdapter();
        String responseCode = String.valueOf(adapter.getCode(response));
        String responseStatus =
            adapter.getSuccess(response) ? LogConstant.SUCCESS : LogConstant.FAILURE;
        long elapsedTime = globalContext.getRequestCostTime();
        adapter.setElapsedTime(response, elapsedTime);
        ThreadContext.put(LogConstant.RESPONSE_CODE, responseCode);
        ThreadContext.put(LogConstant.RESPONSE_STATUS, responseStatus);
        ThreadContext.put(LogConstant.ELAPSED_TIME, elapsedTime + "ms");
    }

    private String format(String str) {
        if (str == null) {
            return "";
        }
        return StringUtils.abbreviate(str, 512);
    }

    private String format(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        if (map.isEmpty()) {
            return "";
        }
        return map.entrySet().stream().sorted(Entry.comparingByKey())
            .map(entry -> format(entry.getKey()) + "=" + format(entry.getValue().toString()))
            .collect(Collectors.joining(","));
    }
}