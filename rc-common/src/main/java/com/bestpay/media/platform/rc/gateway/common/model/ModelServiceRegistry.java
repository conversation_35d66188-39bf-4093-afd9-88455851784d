package com.bestpay.media.platform.rc.gateway.common.model;

import com.github.phantomthief.failover.impl.PriorityFailover.ResStatus;
import com.github.phantomthief.failover.impl.PriorityFailoverManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
@Slf4j
public class ModelServiceRegistry<M extends ModelService> {

    private final PriorityFailoverManager<M> failoverManager;
    private final ModelServiceChecker<M> checker;
    private final M failover;

    public ModelServiceRegistry(PriorityFailoverManager<M> failoverManager,
        ModelServiceChecker<M> checker, M failover) {
        this.failoverManager = failoverManager;
        this.checker = checker;
        this.failover = failover;
    }

    public M getModelService() {
        return failover;
    }

    public Map<M, String> getVersions() {
        return failoverManager.getFailover().getAll().stream()
            .collect(HashMap::new, (map, node) -> map.put(node, checker.version(node)), Map::putAll);
    }

    public List<Pair<M, ResStatus>> getLoadStatuses() {
        return failoverManager.getFailover().getAll().stream()
            .map(modelService -> Pair.of(modelService,
                failoverManager.getFailover().getResourceStatus(modelService)))
            .filter(pair -> Objects.nonNull(pair.getRight()))
            .collect(Collectors.toList());
    }

    public double getLoadRate() {
        List<Pair<M, ResStatus>> statuses = getLoadStatuses();
        if (statuses.isEmpty()) {
            return 0;
        }
        List<ResStatus> resStatuses = statuses.stream().map(Pair::getRight)
            .collect(Collectors.toList());
        double total = resStatuses.stream().mapToDouble(ResStatus::getMaxWeight).sum();
        double available = resStatuses.stream().mapToDouble(ResStatus::getCurrentWeight).sum();
        return available / total;
    }

    public double getLoadWeight() {
        List<Pair<M, ResStatus>> statuses = getLoadStatuses();
        if (statuses.isEmpty()) {
            return 0;
        }
        List<ResStatus> resStatuses = statuses.stream().map(Pair::getRight)
            .collect(Collectors.toList());
        return resStatuses.stream().mapToDouble(ResStatus::getMaxWeight).sum();
    }
}
