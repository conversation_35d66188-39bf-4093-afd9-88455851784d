package com.bestpay.media.platform.rc.gateway.common.vector.model;

import com.bestpay.media.platform.rc.gateway.common.entity.Probability;
import com.bestpay.media.platform.rc.gateway.common.vector.engine.VectorEngine;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2023/5/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReadWriteSearchConfig {

    private List<Probability<VectorEngine>> readEngines;

    private List<Probability<VectorEngine>> writeEngines;

}
