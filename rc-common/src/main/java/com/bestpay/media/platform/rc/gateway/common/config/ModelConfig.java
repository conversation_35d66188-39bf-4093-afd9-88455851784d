package com.bestpay.media.platform.rc.gateway.common.config;

import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "model")
public class ModelConfig {

    private Map<String, ModelServiceConfig> service;

    private NodeConfig globalDefaultConfig;

    private FailoverConfig failover;

    @Data
    public static class FailoverConfig {

        private Boolean enableConcurrencyControl;

        private Boolean enableCheckTaskImmediately;

        private Integer checkTaskInterval;

        private Double failKeepRateOfCurrentWeight;

        private Double successIncreaseRateOfMaxWeight;

        private Integer recoverThreshold;

        private Double downThreshold;
    }


    @Data
    public static class ModelServiceConfig {

        private List<ModelServiceNodeConfig> nodes;

        private NodeConfig serviceDefaultConfig;
    }

    @Data
    public static class ModelServiceNodeConfig {

        private String uri;

        private NodeConfig config;
    }

    @Data
    public static class NodeConfig {

        private String authorization;
        private Integer priority;
        private Double maxWeight;
        private Double minWeight;
        private Double initWeight;
        private Integer maxConcurrency;

        public NodeConfig setIfAbsent(NodeConfig parent) {
            if (parent == null) {
                return this;
            }
            if (authorization == null) {
                authorization = parent.getAuthorization();
            }
            if (priority == null) {
                priority = parent.getPriority();
            }
            if (maxWeight == null) {
                maxWeight = parent.getMaxWeight();
            }
            if (minWeight == null) {
                minWeight = parent.getMinWeight();
            }
            if (initWeight == null) {
                initWeight = parent.getInitWeight();
            }
            if (maxConcurrency == null) {
                maxConcurrency = parent.getMaxConcurrency();
            }
            return this;
        }
    }
}
