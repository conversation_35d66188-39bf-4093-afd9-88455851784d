package com.bestpay.media.platform.rc.gateway.common.vector.service;

import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import java.util.List;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
public interface CollectionService {

    List<Collection> queryCollectionInfoList();

    Collection queryCollectionInfo(String collectionName);

    boolean createCollection(String collectionName, CollectionParam param);

    boolean isCollectionExist(String collectionName);

    boolean deleteCollection(String collectionName);

}
