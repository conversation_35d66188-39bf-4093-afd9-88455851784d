package com.bestpay.media.platform.rc.gateway.common.vector.engine;

import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2023/5/11
 */
public interface VectorEngine {

    List<Collection> queryCollectionInfoList();

    Collection queryCollectionInfo(String collectionName);

    boolean createCollection(String collectionName, CollectionParam param);

    boolean isCollectionExist(String collectionName);

    boolean deleteCollection(String collectionName);

    <V extends Vector> String insertVector(String collectionName, V vector);

    boolean deleteVectorById(String collectionName, String id);

    boolean isVectorExistById(String collectionName, String id);

    <V extends Vector> List<SearchResult<V>> searchVector(String collectionName, Class<V> vectorClass, SearchParam param);

    <V extends Vector> List<V> queryVectorList(String collectionName, Class<V> vectorClass, int page, int limit);

    <V extends Vector> Map<String, Map<String, Long>> queryAggregations(String collectionName, Class<V> vectorClass,
        List<String> fieldNames, int limit);

    <V extends Vector> List<V> queryOldestVectors(String collectionName, Class<V> vectorClass, int offset, int limit);

}
