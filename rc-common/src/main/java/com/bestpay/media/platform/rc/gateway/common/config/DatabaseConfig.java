package com.bestpay.media.platform.rc.gateway.common.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/8/7
 */
@Data
@Component
@ConfigurationProperties(prefix = "database")
@ConditionalOnProperty(prefix = "database", name = "enable", havingValue = "true")
public class DatabaseConfig {

    private String schema;
    private String tableName;
    private String driverClassName;
    private String url;
    private String userName;
    private String password;
    private Boolean enable;
    private Integer maxPoolSize;
    private String poolName;

}
