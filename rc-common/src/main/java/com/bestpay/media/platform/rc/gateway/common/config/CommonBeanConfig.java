package com.bestpay.media.platform.rc.gateway.common.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.bestpay.media.platform.rc.gateway.common.component.BlowfishComponent;
import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.redis.component.RedisPrefixNameMapper;
import com.google.common.base.Strings;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.NameMapper;
import org.redisson.api.RedissonClient;
import org.redisson.config.BaseConfig;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @Author: 段然
 * @Date: 2024/3/30
 */
@Slf4j
@Configuration
@EnableConfigurationProperties
public class CommonBeanConfig {

    private final BlowfishComponent blowfishComponent;

    @Autowired
    public CommonBeanConfig(BlowfishComponent blowfishComponent) {
        this.blowfishComponent = blowfishComponent;
    }

    @Bean
    public Executor executor(ExecutorConfig config) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(config.getCorePoolSize());
        executor.setMaxPoolSize(config.getMaxPoolSize());
        executor.setAllowCoreThreadTimeOut(config.getAllowCoreThreadTimeOut());
        executor.setKeepAliveSeconds(config.getKeepAliveSeconds());
        executor.setQueueCapacity(config.getQueueCapacity());
        executor.setThreadNamePrefix(config.getThreadNamePrefix());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean
    @SneakyThrows
    public RedissonClient redissonClient(RedisConfig redisConfig) {
        if (Strings.isNullOrEmpty(redisConfig.getConfig())) {
            throw new ConfigInvalidException("redis.config", "配置不能为空");
        }
        Config config = Config.fromYAML(redisConfig.getConfig());
        BaseConfig<?> baseConfig;
        if (config.isClusterConfig()) {
            baseConfig = config.useClusterServers();
        } else if (config.isSentinelConfig()) {
            baseConfig = config.useSentinelServers();
        } else {
            baseConfig = config.useSingleServer();
        }
        decryptPassword(baseConfig);
        NameMapper nameMapper = new RedisPrefixNameMapper(redisConfig.getGlobalPrefix());
        baseConfig.setNameMapper(nameMapper);
        return Redisson.create(config);
    }

    private void decryptPassword(BaseConfig<?> config) {
        if (!Strings.isNullOrEmpty(config.getPassword())) {
            config.setPassword(blowfishComponent.tryDecrypt("redis.config.?.password", config.getPassword()));
        }
        if (!Strings.isNullOrEmpty(config.getUsername())) {
            config.setUsername(blowfishComponent.tryDecrypt("redis.config.?.username", config.getUsername()));
        }
    }

}
