package com.bestpay.media.platform.rc.gateway.common.model.component;

import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceChecker;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: 段然
 * @Date: 2024/3/22
 */
@Slf4j
public class UnsupportedHealthChecker implements ModelServiceChecker<ModelService> {

    @Override
    public boolean check(ModelService modelService) {
        log.warn("该模型服务不支持健康检查: {}", modelService.getClass());
        return false;
    }

    @Override
    public String version(ModelService modelService) {
        log.warn("该模型服务不支持版本检查: {}", modelService.getClass());
        return "unsupported";
    }
}
