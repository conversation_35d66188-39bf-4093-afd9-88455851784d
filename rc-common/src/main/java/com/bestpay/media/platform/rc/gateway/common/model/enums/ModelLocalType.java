package com.bestpay.media.platform.rc.gateway.common.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 本地模型类型
 *
 * <AUTHOR> 2021/1/19
 */

@Getter
@AllArgsConstructor
public enum ModelLocalType {

    /**
     * ONNX模型
     */
    ONNX(new String[]{"file", "jar"}, new String[]{".onnx"}),
    /**
     * TensorFlow模型
     */
    TENSORFLOW(new String[]{"file", "jar"}, new String[]{".pb"}),
    /**
     * PyTorch模型
     */
    PYTORCH(new String[]{"file", "jar"}, new String[]{".pt"}),
    ;

    private final String[] schemes;

    private final String[] extension;

}
