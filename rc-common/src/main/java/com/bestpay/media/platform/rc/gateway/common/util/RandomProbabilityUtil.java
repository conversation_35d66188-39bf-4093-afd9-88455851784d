package com.bestpay.media.platform.rc.gateway.common.util;

import com.bestpay.media.platform.rc.gateway.common.entity.Probability;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;

/**
 * 概率与别名离散分布抽样算法见：https://www.keithschwarz.com/darts-dice-coins/
 *
 * @Author: 段然
 * @Date: 2024/8/27
 */
@UtilityClass
public class RandomProbabilityUtil {

    public static <T> Probability<T> getRandom(List<Probability<T>> probabilities) {
        List<Probability<T>> validProbabilities = filterValidPaths(probabilities);
        // 使用ThreadLocalRandom创建随机数以提高并发性能
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int column = random.nextInt(validProbabilities.size());
        boolean coinToss = random.nextDouble() < validProbabilities.get(column).getProbability();
        int index = coinToss ? column : validProbabilities.get(column).getAlias();
        return validProbabilities.get(index);
    }

    public static <T> void calculateAliasMethod(List<Probability<T>> probabilities) {
        List<Probability<T>> validProbabilities = filterValidPaths(probabilities);
        int size = validProbabilities.size();
        List<Integer> weights = validProbabilities.stream().map(Probability::getWeight).collect(Collectors.toList());
        List<Double> probabilityList = new ArrayList<>(size);
        List<Integer> aliasList = new ArrayList<>(size);
        calculateAliasMethod(weights, probabilityList, aliasList);
        for (int i = 0; i < size; i++) {
            Probability<T> probability = validProbabilities.get(i);
            probability.setProbability(probabilityList.get(i));
            probability.setAlias(aliasList.get(i));
        }
    }

    /**
     * 过滤权重小于等于0
     */
    private <T> List<Probability<T>> filterValidPaths(List<Probability<T>> probabilities) {
        return probabilities.stream().filter(percentRule -> percentRule.getWeight() > 0).collect(Collectors.toList());
    }

    private static void calculateAliasMethod(List<Integer> weights, List<Double> probabilityList, List<Integer> aliasList) {
        double sum = 0;
        int size = weights.size();
        int[] alias = new int[size];
        double[] probability = new double[size];
        List<Double> probabilities = new ArrayList<>(size);
        for (Integer weight : weights) {
            if (weight > 0) {
                sum += weight;
            }
        }
        for (Integer weight : weights) {
            if (weight > 0) {
                probabilities.add(weight / sum);
            }
        }
        double average = 1.0 / size;
        Deque<Integer> small = new ArrayDeque<>();
        Deque<Integer> large = new ArrayDeque<>();
        for (int i = 0; i < size; ++i) {
            if (probabilities.get(i) >= average) {
                large.add(i);
            } else {
                small.add(i);
            }
        }
        while (!small.isEmpty() && !large.isEmpty()) {
            int less = small.removeLast();
            int more = large.removeLast();
            probability[less] = probabilities.get(less) * size;
            alias[less] = more;
            probabilities.set(more, probabilities.get(more) + probabilities.get(less) - average);
            if (probabilities.get(more) >= average) {
                large.add(more);
            } else {
                small.add(more);
            }
        }
        while (!small.isEmpty()) {
            probability[small.removeLast()] = 1.0;
        }
        while (!large.isEmpty()) {
            probability[large.removeLast()] = 1.0;
        }
        aliasList.clear();
        for (int a : alias) {
            aliasList.add(a);
        }
        probabilityList.clear();
        for (double p : probability) {
            probabilityList.add(p);
        }
    }


}
