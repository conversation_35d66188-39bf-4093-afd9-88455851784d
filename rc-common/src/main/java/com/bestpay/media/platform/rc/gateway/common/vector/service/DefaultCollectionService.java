package com.bestpay.media.platform.rc.gateway.common.vector.service;

import com.bestpay.media.platform.rc.gateway.common.vector.engine.VectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import java.util.List;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
public class DefaultCollectionService implements CollectionService {

    private final VectorEngine vectorEngine;

    public DefaultCollectionService(VectorEngine vectorEngine) {
        this.vectorEngine = vectorEngine;
    }

    @Override
    public List<Collection> queryCollectionInfoList() {
        return vectorEngine.queryCollectionInfoList();
    }

    @Override
    public Collection queryCollectionInfo(String collectionName) {
        return vectorEngine.queryCollectionInfo(collectionName);
    }

    @Override
    public boolean createCollection(String collectionName, CollectionParam param) {
        return vectorEngine.createCollection(collectionName, param);
    }

    @Override
    public boolean isCollectionExist(String collectionName) {
        return vectorEngine.isCollectionExist(collectionName);
    }

    @Override
    public boolean deleteCollection(String collectionName) {
        return vectorEngine.deleteCollection(collectionName);
    }
}
