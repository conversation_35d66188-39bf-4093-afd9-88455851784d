package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import com.bestpay.media.platform.rc.gateway.common.entity.Response;
import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.util.ResponseUtil;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Component
public class BaseResponseAdapter implements ResponseAdapter<Response<Object>> {

    @Override
    public int getCode(Response<Object> response) {
        return response.getCode();
    }

    @Override
    public boolean getSuccess(Response<Object> response) {
        return response.getSuccess();
    }

    @Override
    public void setElapsedTime(Response<Object> response, long elapsedTime) {
        response.setElapsed(elapsedTime);
    }

    @Override
    public Response<Object> onError(GatewayError gatewayError, Object... args) {
        return ResponseUtil.onError(gatewayError, args);
    }

    @Override
    public Response<Object> onException(GateWayException e) {
        return ResponseUtil.onException(e);
    }
}
