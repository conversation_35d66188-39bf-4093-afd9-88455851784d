package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;

/**
 * @Author: 段然
 * @Date: 2024/4/3
 */
public interface ResponseAdapter<R> extends Adapter<R> {

    int getCode(R response);

    boolean getSuccess(R response);

    void setElapsedTime(R response, long elapsedTime);

    R onError(GatewayError gatewayError, Object... args);

    R onException(GateWayException e);
}
