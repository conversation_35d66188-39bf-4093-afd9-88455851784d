package com.bestpay.media.platform.rc.gateway.common.util;

import java.lang.annotation.Annotation;
import lombok.experimental.UtilityClass;
import org.springframework.web.method.HandlerMethod;

/**
 * @Author: 段然
 * @Date: 2024/3/21
 */
@UtilityClass
public class ClassUtil {

    public static Class<?>[] getInterfaces(Class<?> clazz) {
        if (!clazz.isInterface()) {
            return clazz.getInterfaces();
        }
        Class<?>[] interfaces = clazz.getInterfaces();
        Class<?>[] interfacesWithItself = new Class[interfaces.length + 1];
        System.arraycopy(interfaces, 0, interfacesWithItself, 0, interfaces.length);
        interfacesWithItself[interfaces.length] = clazz;
        return interfacesWithItself;
    }

    public static <A extends Annotation> A getAnnotation(HandlerMethod handlerMethod,
        Class<A> annotationClass) {
        A annotation = handlerMethod.getMethod().getAnnotation(annotationClass);
        return annotation == null
            ? handlerMethod.getBeanType().getAnnotation(annotationClass)
            : annotation;
    }
}
