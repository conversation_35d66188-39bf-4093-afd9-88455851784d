package com.bestpay.media.platform.rc.gateway.common.model.factory.local;

import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/3/19
 */
@Component
public class PytorchModeServiceFactory {

    @SneakyThrows
    public <M extends ModelService> M getModelService(ModelServiceNodeInfoConfig<M> info) {
        throw new ConfigInvalidException("model.service.?.nodes.uri", "该版本不支持加载pytorch模型");

    }

}
