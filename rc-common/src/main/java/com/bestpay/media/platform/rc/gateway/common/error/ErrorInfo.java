package com.bestpay.media.platform.rc.gateway.common.error;

import com.google.common.base.Strings;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: 段然
 * @Date: 2024/4/10
 */
@Getter
@Setter
public class ErrorInfo {

    private int code;
    private String help;
    private String message;

    private ErrorInfo() {
    }

    public static ErrorInfo of(GatewayError error, Object... args) {
        GatewayErrorType errorType = error.getErrorType();
        ErrorInfo errorInfo = new ErrorInfo();
        errorInfo.setMessage(errorType.getMessage());
        errorInfo.setCode(errorType.getHttpStatusCode());
        errorInfo.setHelp(error.formatHelp(args));
        return errorInfo;
    }

    public String getExceptionMessage() {
        if (Strings.isNullOrEmpty(help)) {
            return message;
        }
        return message + "," + help;
    }

    public void overrideMessage(String message) {
        this.message = message;
    }

    public void overrideHelp(String help) {
        this.help = help;
    }

    public void overrideCode(int code) {
        this.code = code;
    }

}
