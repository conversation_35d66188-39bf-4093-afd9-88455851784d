package com.bestpay.media.platform.rc.gateway.common.vector.component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

/**
 * 信任所有证书
 *
 * @Author: 段然
 * @Date: 2024/9/4
 */
@UtilityClass
public class UnsafeSSLContextFactory {

    private static final String PROTOCOL = "TLS";

    @SneakyThrows
    public static SSLContext getUnsafeSSLContext() {
        SSLContext sslContext = SSLContext.getInstance(PROTOCOL);
        sslContext.init(null, new TrustManager[]{new UnsafeTrustManager()}, null);
        return sslContext;
    }

}
