package com.bestpay.media.platform.rc.gateway.common.config;

import com.bestpay.media.platform.rc.gateway.common.component.BlowfishComponent;
import com.bestpay.media.platform.rc.gateway.common.config.VectorEngineConfig.VectorEngineGroupConfig;
import com.bestpay.media.platform.rc.gateway.common.config.VectorEngineConfig.VectorEngineGroupConfig.VectorEngineNodeConfig;
import com.bestpay.media.platform.rc.gateway.common.entity.Probability;
import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.util.RandomProbabilityUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.engine.ElasticSearchVectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.engine.ReadWriteVectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.engine.VectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.enums.EngineMode;
import com.bestpay.media.platform.rc.gateway.common.vector.enums.EngineType;
import com.bestpay.media.platform.rc.gateway.common.vector.model.ElasticSearchConfig;
import com.bestpay.media.platform.rc.gateway.common.vector.model.ReadWriteSearchConfig;
import com.bestpay.media.platform.rc.gateway.common.vector.service.DefaultVectorEngineServiceProvider;
import com.bestpay.media.platform.rc.gateway.common.vector.service.VectorEngineServiceProvider;
import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.HttpHost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: 段然
 * @Date: 2024/4/1
 */
@Slf4j
@Configuration
public class VectorEngineBeanConfig {

    private final BlowfishComponent blowfishComponent;

    @Autowired
    public VectorEngineBeanConfig(BlowfishComponent blowfishComponent) {
        this.blowfishComponent = blowfishComponent;
    }

    public static EngineType parseType(int index, String type) {
        if (Strings.isNullOrEmpty(type)) {
            return EngineType.READ_WRITE;
        }
        for (EngineType engineType : EngineType.values()) {
            if (engineType.name().equalsIgnoreCase(type)) {
                return engineType;
            }
        }
        throw new ConfigInvalidException("vector-engine.group.[" + index + "].type",
            "特征库群组权限类型不支持: " + type + ", 可选值为: " + Arrays.toString(EngineType.values()));
    }

    public static EngineMode parseMode(String mode) {
        if (Strings.isNullOrEmpty(mode)) {
            return EngineMode.DEFAULT;
        }
        for (EngineMode engineMode : EngineMode.values()) {
            if (engineMode.name().equalsIgnoreCase(mode)) {
                return engineMode;
            }
        }
        throw new ConfigInvalidException("vector-engine.mode",
            "特征库引擎模式不支持: " + mode + ", 可选值为: " + Arrays.toString(EngineMode.values()));
    }

    @Bean
    public VectorEngine vectorSearchEngine(VectorEngineConfig vectorEngineConfig) {
        if (vectorEngineConfig.getGroup() == null || vectorEngineConfig.getGroup().isEmpty()) {
            throw new ConfigInvalidException("vector-engine.group", "特征库群组配置不能为空");
        }
        EngineMode mode = parseMode(vectorEngineConfig.getMode());
        return mode == EngineMode.DEFAULT ?
            createDefaultEngine(vectorEngineConfig) :
            createMultiWriteEngine(vectorEngineConfig);
    }

    @Bean
    public VectorEngineServiceProvider vectorEngineServiceProvider(VectorEngine vectorEngine) {
        return new DefaultVectorEngineServiceProvider(vectorEngine);
    }

    private VectorEngine createMultiWriteEngine(VectorEngineConfig vectorEngineConfig) {
        if (vectorEngineConfig.getGroup().size() < 2) {
            throw new ConfigInvalidException("vector-engine.group", "特征库在多写模式下，群组数量必须大于1");
        }
        List<VectorEngineGroupConfig> groupList = vectorEngineConfig.getGroup();
        List<Triple<Integer, Integer, VectorEngine>> readEngines = new ArrayList<>(groupList.size());
        List<Triple<Integer, Integer, VectorEngine>> writeEngines = new ArrayList<>(groupList.size());
        for (int i = 0; i < groupList.size(); i++) {
            VectorEngineGroupConfig group = groupList.get(i);
            EngineType engineType = parseType(i, group.getType());
            int weight = ObjectUtils.firstNonNull(group.getWeight(), vectorEngineConfig.getDefaultWeight());
            VectorEngine engine = createEngine(i, vectorEngineConfig.getPrefix(), group);
            Triple<Integer, Integer, VectorEngine> engineTriple = Triple.of(i, weight, engine);
            if (engineType == EngineType.READ || engineType == EngineType.READ_WRITE) {
                readEngines.add(engineTriple);
            }
            if (engineType == EngineType.WRITE || engineType == EngineType.READ_WRITE) {
                writeEngines.add(engineTriple);
            }
        }
        if (readEngines.isEmpty()) {
            throw new ConfigInvalidException("vector-engine.group", "特征库在多写模式下，必须有至少一个群组拥有读权限");
        }
        if (writeEngines.isEmpty()) {
            throw new ConfigInvalidException("vector-engine.group", "特征库在多写模式下，必须有至少一个特征库群组拥有写权限");
        }
        log.info("特征库使用多写模式，读取群组配置为: {}",
            readEngines.stream()
                .map(pair -> "[" + pair.getLeft() + "]:[" + groupList.get(pair.getLeft()).getNodes()
                    .stream()
                    .map(node -> node.getHost() + ":" + node.getPort()).collect(
                        Collectors.joining(",")) + "]")
                .collect(Collectors.joining(";")));
        log.info("特征库使用多写模式，写入群组配置为: {}",
            writeEngines.stream()
                .map(pair -> "[" + pair.getLeft() + "]:[" + groupList.get(pair.getLeft()).getNodes()
                    .stream()
                    .map(node -> node.getHost() + ":" + node.getPort()).collect(
                        Collectors.joining(",")) + "]")
                .collect(Collectors.joining(";")));
        ReadWriteSearchConfig config = new ReadWriteSearchConfig();
        config.setReadEngines(calculateProbability(readEngines));
        config.setWriteEngines(calculateProbability(writeEngines));
        return new ReadWriteVectorEngine(config);
    }

    private List<Probability<VectorEngine>> calculateProbability(List<Triple<Integer, Integer, VectorEngine>> engines) {
        List<Probability<VectorEngine>> probabilities = new ArrayList<>(engines.size());
        for (Triple<Integer, Integer, VectorEngine> engine : engines) {
            Probability<VectorEngine> probability = new Probability<>();
            probability.setWeight(engine.getMiddle());
            probability.setTarget(engine.getRight());
            probabilities.add(probability);
        }
        RandomProbabilityUtil.calculateAliasMethod(probabilities);
        return probabilities;
    }

    private VectorEngine createDefaultEngine(VectorEngineConfig vectorEngineConfig) {
        if (vectorEngineConfig.getGroup().size() != 1) {
            throw new ConfigInvalidException("vector-engine.group", "特征库在默认模式下，群组数量必须为1");
        }
        VectorEngineGroupConfig group = vectorEngineConfig.getGroup().get(0);
        log.info("特征库使用默认模式，群组配置: {}",
            group.getNodes().stream().map(node -> node.getHost() + ":" + node.getPort()).collect(Collectors.joining(",")));
        return createEngine(0, vectorEngineConfig.getPrefix(), group);
    }

    private VectorEngine createEngine(int index, String prefix, VectorEngineGroupConfig group) {
        List<VectorEngineNodeConfig> nodes = group.getNodes();
        if (nodes == null || nodes.isEmpty()) {
            throw new ConfigInvalidException("vector-engine.group.[" + index + "].nodes", "特征库节点配置不能为空");
        }
        HttpHost[] hosts = new HttpHost[nodes.size()];
        for (int i = 0, nodesSize = nodes.size(); i < nodesSize; i++) {
            VectorEngineNodeConfig node = nodes.get(i);
            int port;
            try {
                port = Integer.parseInt(node.getPort());
            } catch (NumberFormatException e) {
                throw new ConfigInvalidException("vector-engine.group.[" + index + "].nodes.[" + i + "].port", "特征库节点端口号必须为数字");
            }
            if (port <= 0 || port > 65535) {
                throw new ConfigInvalidException("vector-engine.group.[" + index + "].nodes.[" + i + "].port",
                    "特征库节点端口号必须在1到65535之间");
            }
            hosts[i] = HttpHost.create(node.getHost() + ":" + port);
        }
        ElasticSearchConfig config = new ElasticSearchConfig();
        config.setHosts(hosts);
        config.setPrefix(prefix);
        config.setIgnoreCert(group.getIgnoreCert());
        config.setUsername(blowfishComponent.tryDecrypt("vector-engine.group.[" + index + "].username", group.getUsername()));
        config.setPassword(blowfishComponent.tryDecrypt("vector-engine.group.[" + index + "].password", group.getPassword()));
        return new ElasticSearchVectorEngine(config);
    }
}
