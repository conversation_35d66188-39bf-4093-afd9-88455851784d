package com.bestpay.media.platform.rc.gateway.common.error;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.helpers.MessageFormatter;

/**
 * @Author: 段然
 * @Date: 2024/4/10
 */
@Slf4j
@Getter
public enum GatewayError {
    //请求参数错误
    INVALID_PARAM(GatewayErrorType.INVALID_PARAM),
    INVALID_PARAM_MISSING_REQUEST_VALUE_EXCEPTION(GatewayErrorType.INVALID_PARAM, "POST请求请求体不能为空"),
    INVALID_PARAM_HTTP_MESSAGE_NOT_READABLE_EXCEPTION(GatewayErrorType.INVALID_PARAM,
        "请求参数解析失败，请检查请求参数是否为正确的json格式"),
    INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_SUPPORTED_EXCEPTION(GatewayErrorType.INVALID_PARAM, "请求头Content-Type:{}不支持"),
    INVALID_PARAM_HTTP_MEDIA_TYPE_NOT_ACCEPTABLE_EXCEPTION(GatewayErrorType.INVALID_PARAM, "请求头Accept不支持，支持的类型为{}"),
    INVALID_PARAM_HTTP_REQUEST_METHOD_NOT_SUPPORTED_EXCEPTION(GatewayErrorType.INVALID_PARAM, "请求方法{}不支持"),
    INVALID_PARAM_MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION(GatewayErrorType.INVALID_PARAM, "请求参数{}不能为空"),
    INVALID_PARAM_DUPLICATE_CUSTOMER_NO(GatewayErrorType.INVALID_PARAM, "客户号custom_no不能重复"),
    INVALID_PARAM_DUPLICATE_RPF_SERIAL_NO(GatewayErrorType.INVALID_PARAM, "请求流水号rpfserialno不能重复"),
    INVALID_PARAM_COLLECTION_NOT_EXIST(GatewayErrorType.INVALID_PARAM, "特征库{}不存在，请检查特征库是否存在"),
    INVALID_PARAM_COLLECTION_NAME_EMPTY(GatewayErrorType.INVALID_PARAM, "特征库名称collectionName不能为空"),
    INVALID_PARAM_COLLECTION_ALREADY_EXISTS(GatewayErrorType.INVALID_PARAM,
        "特征库{}已存在，请检查特征库是否已创建，如需创建新特征库，请使用不同的名称，如需清空特征库，请先删除特征库"),
    INVALID_PARAM_COLLECTION_NAME_INVALID(GatewayErrorType.INVALID_PARAM,
        "特征库名称collectionName无效，请检查特征库名称是否符合规范，特征库名称必须为小写字母、数字或下划线的字符串，且必须以小写字母开头"),
    //接口不存在
    NOT_FOUND(GatewayErrorType.NOT_FOUND),
    //请求方法错误
    NOT_ALLOWED(GatewayErrorType.NOT_ALLOWED),
    //调用外部服务错误
    BAD_GATEWAY(GatewayErrorType.BAD_GATEWAY),
    BAD_GATEWAY_ELASTICSEARCH_SECURITY_EXCEPTION(GatewayErrorType.BAD_GATEWAY,
        "elasticsearch无法验证用户，请检查application.yml以确保elasticsearch用户名和密码已正确配置"),
    BAD_GATEWAY_ELASTICSEARCH_EXCEPTION(GatewayErrorType.BAD_GATEWAY, "elasticsearch服务异常，请联系技术支持"),
    BAD_GATEWAY_ELASTICSEARCH_IO_EXCEPTION(GatewayErrorType.BAD_GATEWAY,
        "无法连接elasticsearch，请检查application.yml以确保elasticsearch主机和端口已正确配置，且网络联通"),
    BAD_GATEWAY_PARSE_RESPONSE_FAILED(GatewayErrorType.BAD_GATEWAY, "解析模型服务响应结果失败，请联系技术支持"),
    //调用外部服务超时
    GATEWAY_TIMEOUT(GatewayErrorType.GATEWAY_TIMEOUT),
    //服务繁忙
    TOO_MANY_REQUEST(GatewayErrorType.TOO_MANY_REQUEST),
    //服务内部错误
    INTERNAL_SERVER_ERROR(GatewayErrorType.INTERNAL_SERVER_ERROR),
    INTERNAL_SERVER_ERROR_WRITE_ENGINE_FAILED(GatewayErrorType.INTERNAL_SERVER_ERROR, "特征库写引擎任务执行失败"),
    INTERNAL_SERVER_ERROR_INVALID_CONFIG(GatewayErrorType.INTERNAL_SERVER_ERROR, "配置文件{}错误:{}"),
    ;

    private static final String[] DEFAULT_ARGS = {""};
    private final GatewayErrorType errorType;
    private final String message;
    private final String help;

    GatewayError(GatewayErrorType errorType, String help) {
        this.message = errorType.getMessage();
        this.errorType = errorType;
        this.help = help;
    }

    GatewayError(GatewayErrorType errorType) {
        this(errorType, errorType.getDefaultHelp());
    }

    public String formatHelp(Object... args) {
        return args == null || args.length == 0 ?
            MessageFormatter.basicArrayFormat(help, DEFAULT_ARGS) :
            MessageFormatter.basicArrayFormat(help, args);
    }

}
