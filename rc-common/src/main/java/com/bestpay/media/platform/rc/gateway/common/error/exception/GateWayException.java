package com.bestpay.media.platform.rc.gateway.common.error.exception;

import com.bestpay.media.platform.rc.gateway.common.error.ErrorInfo;
import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;

public class GateWayException extends ErrorInfoException {

    public GateWayException(GatewayError gatewayError, Throwable cause, Object... args) {
        super(ErrorInfo.of(gatewayError, args), cause);
    }

    public GateWayException(GatewayError gatewayError, Object... args) {
        super(ErrorInfo.of(gatewayError, args));
    }

}
