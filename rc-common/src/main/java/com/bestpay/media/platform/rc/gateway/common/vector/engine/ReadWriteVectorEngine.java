package com.bestpay.media.platform.rc.gateway.common.vector.engine;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.INTERNAL_SERVER_ERROR_WRITE_ENGINE_FAILED;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.bestpay.media.platform.rc.gateway.common.entity.Probability;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.util.RandomProbabilityUtil;
import com.bestpay.media.platform.rc.gateway.common.vector.component.MultiWriteTask;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Collection;
import com.bestpay.media.platform.rc.gateway.common.vector.model.CollectionParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.ReadWriteSearchConfig;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 读写分离搜索引擎
 *
 * @Author: 段然
 * @Date: 2024/3/21
 */
@Slf4j
public class ReadWriteVectorEngine implements VectorEngine {

    private final List<Probability<VectorEngine>> readEngines;
    private final List<Probability<VectorEngine>> writeEngines;
    private final ExecutorService writeExecutor;

    public ReadWriteVectorEngine(ReadWriteSearchConfig config) {
        this.readEngines = config.getReadEngines();
        this.writeEngines = config.getWriteEngines();
        this.writeExecutor = TtlExecutors.getTtlExecutorService(
            Executors.newFixedThreadPool(writeEngines.size()));
    }

    @SneakyThrows
    private <R> R executeRead(MultiWriteTask<R> task) {
        return task.execute(RandomProbabilityUtil.getRandom(readEngines).getTarget());
    }

    @SneakyThrows
    private <R> R executeWrite(MultiWriteTask<R> task) {
        // Attempt to submit the task to all write engines and gather the results.
        // If an exception occurs with one engine, log it and move on to the next.
        // However, if every write engine fails, throw the first exception encountered.
        Throwable[] holder = new Throwable[1];
        List<R> results = writeEngines.parallelStream()
            .map(Probability::getTarget)
            .map(engine -> (Callable<R>) () -> task.execute(engine))
            .map(writeExecutor::submit)
            .map(future -> getResult(future, holder))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        if (results.isEmpty() && holder[0] != null) {
            throw new GateWayException(INTERNAL_SERVER_ERROR_WRITE_ENGINE_FAILED, holder[0]);
        }
        return results.isEmpty() ? null : results.get(0);
    }

    private <R> R getResult(Future<R> future, Throwable[] holder) {
        try {
            return future.get();
        } catch (Throwable e) {
            log.error("特征库写入任务失败", e);
            synchronized (holder) {
                if (holder[0] == null) {
                    holder[0] = e instanceof ExecutionException && e.getCause() != null ? e.getCause() : e;
                }
            }
            return null;
        }
    }

    private static boolean nullToFalse(Boolean result) {
        return Boolean.TRUE.equals(result);
    }

    public List<Collection> queryCollectionInfoList() {
        return executeRead(VectorEngine::queryCollectionInfoList);
    }

    public Collection queryCollectionInfo(String collectionName) {
        return executeRead(engine -> engine.queryCollectionInfo(collectionName));
    }

    public boolean createCollection(String collectionName, CollectionParam param) {
        return nullToFalse(executeWrite(engine -> engine.createCollection(collectionName, param)));
    }

    public boolean isCollectionExist(String collectionName) {
        return nullToFalse(executeRead(engine -> engine.isCollectionExist(collectionName)));
    }

    public boolean deleteCollection(String collectionName) {
        return nullToFalse(executeWrite(engine -> engine.deleteCollection(collectionName)));
    }

    public String insertVector(String collectionName, Vector vector) {
        return executeWrite(engine -> engine.insertVector(collectionName, vector));
    }


    public boolean deleteVectorById(String collectionName, String id) {
        return nullToFalse(executeWrite(engine -> engine.deleteVectorById(collectionName, id)));
    }


    public boolean isVectorExistById(String collectionName, String id) {
        return nullToFalse(executeRead(engine -> engine.isVectorExistById(collectionName, id)));
    }

    @Override
    public <V extends Vector> List<SearchResult<V>> searchVector(String collectionName, Class<V> vectorClass, SearchParam param) {
        return executeRead(engine -> engine.searchVector(collectionName, vectorClass, param));
    }

    @Override
    public <V extends Vector> List<V> queryVectorList(String collectionName, Class<V> vectorClass, int page, int limit) {
        return executeRead(engine -> engine.queryVectorList(collectionName, vectorClass, page, limit));
    }

    @Override
    public <V extends Vector> Map<String, Map<String, Long>> queryAggregations(String collectionName, Class<V> vectorClass,
        List<String> fieldNames, int limit) {
        return executeRead(engine -> engine.queryAggregations(collectionName, vectorClass, fieldNames, limit));
    }

    @Override
    public <V extends Vector> List<V> queryOldestVectors(String collectionName, Class<V> vectorClass, int offset, int limit) {
        return executeRead(engine -> engine.queryOldestVectors(collectionName, vectorClass, offset, limit));
    }

}
