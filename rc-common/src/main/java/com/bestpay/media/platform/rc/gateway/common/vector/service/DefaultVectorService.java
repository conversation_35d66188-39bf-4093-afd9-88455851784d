package com.bestpay.media.platform.rc.gateway.common.vector.service;

import com.bestpay.media.platform.rc.gateway.common.vector.engine.VectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchParam;
import com.bestpay.media.platform.rc.gateway.common.vector.model.SearchResult;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
@AllArgsConstructor
public class DefaultVectorService<V extends Vector> implements VectorService<V> {

    private final VectorEngine vectorEngine;
    private final String collectionName;
    private final Class<V> vectorClass;

    @Override
    public String insertVector(V vector) {
        return vectorEngine.insertVector(collectionName, vector);
    }

    @Override
    public boolean deleteVectorById(String id) {
        return vectorEngine.deleteVectorById(collectionName, id);
    }

    @Override
    public boolean isVectorExistById(String id) {
        return vectorEngine.isVectorExistById(collectionName, id);
    }

    @Override
    public List<SearchResult<V>> searchVector(SearchParam param) {
        return vectorEngine.searchVector(collectionName, vectorClass, param);
    }

    @Override
    public List<V> queryVectorList(int page, int limit) {
        return vectorEngine.queryVectorList(collectionName, vectorClass, page, limit);
    }

    @Override
    public Map<String, Map<String, Long>> queryAggregations(List<String> fieldNames, int limit) {
        return vectorEngine.queryAggregations(collectionName, vectorClass, fieldNames, limit);
    }

    @Override
    public List<V> queryOldestVectors(int offset, int limit) {
        return vectorEngine.queryOldestVectors(collectionName, vectorClass, offset, limit);
    }

}
