package com.bestpay.media.platform.rc.gateway.common.component;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceManager;
import com.bestpay.media.platform.rc.gateway.common.model.ModelServiceRegistry;
import com.bestpay.media.platform.rc.gateway.common.util.ResourceFileUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/9/12
 */
@Slf4j
@Component
public class VersionManager {

    public static final String GATEWAY_VERSION_KEY = "gateway";

    public static final String SERVICE_VERSION_KEY = "service";

    private static final String VERSION_JSON_PATH = "/version.json";

    private final LoadingCache<String, JSONObject> versionCache;

    private final ModelServiceManager modelServiceManager;

    private final String versionInfo;

    @SneakyThrows
    public VersionManager(ModelServiceManager modelServiceManager) {
        this.modelServiceManager = modelServiceManager;
        this.versionInfo = ResourceFileUtil.readString(VERSION_JSON_PATH);
        this.versionCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(this::getModelServiceVersion);
    }

    public String getVersion() {
        return getVersionJson().toJSONString(Feature.PrettyFormat);
    }

    public JSONObject getVersionJson() {
        JSONObject versionJson = versionCache.get(VERSION_JSON_PATH);
        return versionJson == null ? new JSONObject() : versionJson;
    }

    private JSONObject getModelServiceVersion(String key) {
        JSONObject versionJson = JSONObject.parseObject(versionInfo);
        Map<Class<? extends ModelService>, ModelServiceRegistry<?>> modelServiceRegistry = modelServiceManager.getAllModelServiceRegistry();
        Map<String, Class<? extends ModelService>> serviceClass = modelServiceManager.getAllModelServiceClass();
        JSONObject modelServiceVersion = new JSONObject();
        versionJson.put(SERVICE_VERSION_KEY, modelServiceVersion);
        serviceClass.forEach((name, clazz) -> {
            Map<? extends ModelService, String> versions = modelServiceRegistry.get(clazz).getVersions();
            JSONObject nodeVersion = new JSONObject();
            versions.forEach((node, version) -> nodeVersion.put(node.toString(), version));
            modelServiceVersion.put(name, nodeVersion);
        });
        return versionJson;
    }
}
