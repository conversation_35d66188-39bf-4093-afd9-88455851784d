package com.bestpay.media.platform.rc.gateway.common.model.component;

import com.bestpay.media.platform.rc.gateway.common.config.ModelConfig.ModelServiceNodeConfig;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: 段然
 * @Date: 2024/3/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelServiceNodeInfoConfig<M extends ModelService> extends ModelServiceNodeConfig {

    private Class<M> modelServiceClass;

    public static <M extends ModelService> ModelServiceNodeInfoConfig<M> of(Class<M> modelServiceClass,
        ModelServiceNodeConfig config) {
        ModelServiceNodeInfoConfig<M> nodeInfo = new ModelServiceNodeInfoConfig<>();
        nodeInfo.setUri(config.getUri());
        nodeInfo.setConfig(config.getConfig());
        nodeInfo.setModelServiceClass(modelServiceClass);
        return nodeInfo;
    }
}
