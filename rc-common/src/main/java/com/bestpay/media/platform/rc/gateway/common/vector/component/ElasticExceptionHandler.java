package com.bestpay.media.platform.rc.gateway.common.vector.component;

import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.BAD_GATEWAY_ELASTICSEARCH_EXCEPTION;
import static com.bestpay.media.platform.rc.gateway.common.error.GatewayError.BAD_GATEWAY_ELASTICSEARCH_IO_EXCEPTION;

import co.elastic.clients.elasticsearch._types.ElasticsearchException;
import co.elastic.clients.elasticsearch._types.ErrorCause;
import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import com.bestpay.media.platform.rc.gateway.common.error.exception.GateWayException;
import com.bestpay.media.platform.rc.gateway.common.vector.enums.ElasticSearchError;
import com.google.common.base.Strings;
import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: 段然
 * @Date: 2024/4/10
 */
@Slf4j
@UtilityClass
public class ElasticExceptionHandler {

    private static GateWayException handleElasticSearchException(ElasticsearchException exception) {
        // TODO 优化统一ES报错时对外的异常信息
        ErrorCause error = exception.error();
        if (error != null) {
            if (error.causedBy() != null && !Strings.isNullOrEmpty(error.causedBy().reason())) {
                log.error("ElasticsearchException caused by:{}", error.causedBy().reason());
            }
            String type = error.type();
            Optional<ElasticSearchError> esErrorOption = Arrays.stream(ElasticSearchError.values())
                .filter(e -> e.getErrorType().equals(type))
                .findFirst();
            if (esErrorOption.isPresent()) {
                ElasticSearchError esError = esErrorOption.get();
                GatewayError gatewayError = esError.getGatewayError();
                Object[] errorArgs = esError.getHandler().apply(error);
                return new GateWayException(gatewayError, exception, errorArgs);
            }
        }
        return unknownException(exception);
    }

    public static GateWayException handle(Exception e) {
        if (e instanceof ElasticsearchException) {
            return handleElasticSearchException((ElasticsearchException) e);
        }
        if (e instanceof IOException) {
            return new GateWayException(BAD_GATEWAY_ELASTICSEARCH_IO_EXCEPTION, e);
        }
        return unknownException(e);
    }

    private static GateWayException unknownException(Exception e) {
        return new GateWayException(BAD_GATEWAY_ELASTICSEARCH_EXCEPTION, e);
    }

}
