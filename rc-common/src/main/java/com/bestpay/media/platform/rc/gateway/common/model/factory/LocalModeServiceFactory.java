package com.bestpay.media.platform.rc.gateway.common.model.factory;

import com.bestpay.media.platform.rc.gateway.common.error.exception.ConfigInvalidException;
import com.bestpay.media.platform.rc.gateway.common.model.ModelService;
import com.bestpay.media.platform.rc.gateway.common.model.component.ModelServiceNodeInfoConfig;
import com.bestpay.media.platform.rc.gateway.common.model.enums.ModelLocalType;
import com.bestpay.media.platform.rc.gateway.common.model.factory.local.OnnxModeServiceFactory;
import com.bestpay.media.platform.rc.gateway.common.model.factory.local.PytorchModeServiceFactory;
import com.bestpay.media.platform.rc.gateway.common.model.factory.local.TensorflowModeServiceFactory;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/3/19
 */
@Component
public class LocalModeServiceFactory {

    private final OnnxModeServiceFactory onnxModeServiceFactory;
    private final PytorchModeServiceFactory pytorchModeServiceFactory;
    private final TensorflowModeServiceFactory tensorflowModeServiceFactory;

    @Autowired
    public LocalModeServiceFactory(
        OnnxModeServiceFactory onnxModeServiceFactory,
        TensorflowModeServiceFactory tensorflowModeServiceFactory,
        PytorchModeServiceFactory pytorchModeServiceFactory) {
        this.onnxModeServiceFactory = onnxModeServiceFactory;
        this.tensorflowModeServiceFactory = tensorflowModeServiceFactory;
        this.pytorchModeServiceFactory = pytorchModeServiceFactory;
    }

    public <M extends ModelService> M getModelService(ModelServiceNodeInfoConfig<M> info) {
        String uri = info.getUri();
        ModelLocalType modelLocalType = determineModelLocalType(uri);
        if (modelLocalType == ModelLocalType.ONNX) {
            return onnxModeServiceFactory.getModelService(info);
        }
        if (modelLocalType == ModelLocalType.TENSORFLOW) {
            return tensorflowModeServiceFactory.getModelService(info);
        }
        if (modelLocalType == ModelLocalType.PYTORCH) {
            return pytorchModeServiceFactory.getModelService(info);
        }
        throw new ConfigInvalidException("model.service.?.nodes.uri", "不支持的模型uri:" + uri);
    }

    private static ModelLocalType determineModelLocalType(String uriStr) {
        try {
            URI uri = new URI(uriStr);
            return Arrays.stream(ModelLocalType.values())
                .filter(type -> Arrays.stream(type.getSchemes())
                    .filter(s -> s != null && !s.isEmpty())
                    .anyMatch(s -> s.equalsIgnoreCase(uri.getScheme())))
                .findFirst().orElse(null);
        } catch (URISyntaxException e) {
            return null;
        }
    }
}
