package com.bestpay.media.platform.rc.gateway.common.redis;

import com.bestpay.media.platform.rc.gateway.common.config.RedisConfig;
import com.bestpay.media.platform.rc.gateway.common.redis.component.FairSemaphore;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.BatchOptions;
import org.redisson.api.RBatch;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RQueue;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Slf4j
@Component
public class RedisManager {

    private final RedisConfig redisConfig;

    private final RedissonClient redissonClient;

    public RedisManager(RedissonClient redissonClient, RedisConfig redisConfig) {
        this.redissonClient = redissonClient;
        this.redisConfig = redisConfig;
    }

    public RBatch getBatch() {
        return redissonClient.createBatch();
    }

    public RBatch getBatch(BatchOptions options) {
        return redissonClient.createBatch();
    }

    public RLock getLock(String... keys) {
        String key = buildKey(redisConfig.getLockPrefix(), keys);
        return redissonClient.getLock(key);
    }

    public RLock getFairLock(String... keys) {
        String key = buildKey(redisConfig.getLockPrefix(), keys);
        return redissonClient.getFairLock(key);
    }

    public RReadWriteLock getRWLock(String... keys) {
        return redissonClient.getReadWriteLock(buildKey(redisConfig.getReadWriteLockPrefix(), keys));
    }

    public <K, V> RMap<K, V> getMap(String... keys) {
        return redissonClient.getMap(buildKey(redisConfig.getMapPrefix(), keys));
    }

    public <E> RQueue<E> getQueue(String... keys) {
        return redissonClient.getQueue(buildKey(redisConfig.getQueuePrefix(), keys));
    }

    public RSet<String> getSet(String... keys) {
        return redissonClient.getSet(buildKey(redisConfig.getSetPrefix(), keys));
    }

    public RSemaphore getSemaphore(String... keys) {
        return redissonClient.getSemaphore(buildKey(redisConfig.getSemaphorePrefix(), keys));
    }

    public RPermitExpirableSemaphore getPermitExpirableSemaphore(String... keys) {
        return redissonClient.getPermitExpirableSemaphore(buildKey(redisConfig.getSemaphorePrefix(), keys));
    }

    public FairSemaphore getFairSemaphore(String... keys) {
        RPermitExpirableSemaphore permitExpirableSemaphore = redissonClient.getPermitExpirableSemaphore(
            buildKey(redisConfig.getFairSemaphoreSemaphorePrefix(), keys));
        RLock fairLock = redissonClient.getFairLock(
            buildKey(redisConfig.getFairSemaphoreLockPrefix(), keys));
        return new FairSemaphore(redisConfig.getLeaseTime(), permitExpirableSemaphore, fairLock);
    }

    private static String buildKey(String prefix, String... keys) {
        if (keys == null || keys.length == 0) {
            throw new IllegalArgumentException("keys must not be empty");
        }
        return Arrays.stream(keys).collect(Collectors.joining("_", prefix, Strings.EMPTY));
    }

}