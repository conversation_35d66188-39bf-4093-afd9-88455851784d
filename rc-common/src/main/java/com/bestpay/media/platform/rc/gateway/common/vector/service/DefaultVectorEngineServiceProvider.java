package com.bestpay.media.platform.rc.gateway.common.vector.service;

import com.bestpay.media.platform.rc.gateway.common.vector.engine.VectorEngine;
import com.bestpay.media.platform.rc.gateway.common.vector.model.Vector;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: 段然
 * @Date: 2024/9/4
 */
public class DefaultVectorEngineServiceProvider implements VectorEngineServiceProvider {

    private final VectorEngine vectorEngine;

    private final CollectionService collectionService;

    private final Map<String, VectorService<?>> vectorServiceMap;

    public DefaultVectorEngineServiceProvider(VectorEngine vectorEngine) {
        this.vectorEngine = vectorEngine;
        this.collectionService = new DefaultCollectionService(vectorEngine);
        this.vectorServiceMap = new ConcurrentHashMap<>();
    }

    @Override
    public CollectionService getCollectionService() {
        return collectionService;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V extends Vector> VectorService<V> getVectorService(String collectionName, Class<V> vectorClass) {
        String key = buildVectorServiceKey(collectionName, vectorClass);
        VectorService<?> vectorService = vectorServiceMap.get(key);
        if (vectorService == null) {
            vectorService = vectorServiceMap.computeIfAbsent(key,
                k -> new DefaultVectorService<>(vectorEngine, collectionName, vectorClass));
        }
        return (VectorService<V>) vectorService;
    }

    private String buildVectorServiceKey(String collectionName, Class<?> vectorClass) {
        return vectorClass.getName() + "@" + collectionName;
    }
}
