package com.bestpay.media.platform.rc.gateway.common.global.adapter;

import com.bestpay.media.platform.rc.gateway.common.entity.Request;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Component;

/**
 * @Author: 段然
 * @Date: 2024/4/4
 */
@Component
public class BaseRequestAdapter implements RequestAdapter<Request> {

    public BaseRequestAdapter() {
        // init UUID to avoid lazy initialization causing performance issue on first request
        UUID ignored = UUID.randomUUID();
    }

    @Override
    public String getRequestId(Request request) {
        return request.getRequestId() == null ? UUID.randomUUID().toString() : request.getRequestId();
    }

    @Override
    public Map<String, Object> getExtraInfo(Request request) {
        return null;
    }

}
