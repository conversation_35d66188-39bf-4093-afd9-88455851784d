package com.bestpay.media.platform.rc.gateway.common.global;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * <AUTHOR> 2022/3/16
 */
@Slf4j
@Order(30)
@RestControllerAdvice
public class GlobalResponseHandler implements ResponseBodyAdvice<Object> {

    private final GlobalContext globalContext;
    private final GlobalFilter globalFilter;

    @Autowired
    public GlobalResponseHandler(GlobalContext globalContext, GlobalFilter globalFilter) {
        this.globalContext = globalContext;
        this.globalFilter = globalFilter;
    }

    @Override
    public boolean supports(MethodParameter returnType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType,
        MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType,
        ServerHttpRequest request, ServerHttpResponse httpResponse) {
        HttpStatus status = globalFilter.beforeResponse(body);
        globalContext.clear();
        if (status != null) {
            httpResponse.setStatusCode(status);
        }
        return body;
    }


}
