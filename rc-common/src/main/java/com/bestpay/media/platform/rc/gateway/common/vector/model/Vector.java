package com.bestpay.media.platform.rc.gateway.common.vector.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 2023/5/10
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class Vector {

    private String id;

    private List<Float> vector;

    private Long creation_date;

}
