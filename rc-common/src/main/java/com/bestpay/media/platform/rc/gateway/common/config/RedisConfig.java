package com.bestpay.media.platform.rc.gateway.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/15
 */
@Data
@Component
@ConfigurationProperties(prefix = "redis")
public class RedisConfig {

    private Integer leaseTime;
    private String globalPrefix;
    private String lockPrefix;
    private String mapPrefix;
    private String queuePrefix;
    private String readWriteLockPrefix;
    private String setPrefix;
    private String semaphorePrefix;
    private String fairSemaphoreSemaphorePrefix;
    private String fairSemaphoreLockPrefix;
    private String config;

}

