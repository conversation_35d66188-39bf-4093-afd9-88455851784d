package com.bestpay.media.platform.rc.gateway.common.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/1/11
 */
@Data
@Component
@ConfigurationProperties(prefix = "vector-engine")
public class VectorEngineConfig {

    private String prefix;

    private String mode;

    private Integer defaultWeight;

    private List<VectorEngineGroupConfig> group;
    @Data

    public static class VectorEngineGroupConfig {

        private String type;
        private String username;
        private String password;
        private Integer weight;
        private Boolean ignoreCert;
        private List<VectorEngineNodeConfig> nodes;

        @Data
        public static class VectorEngineNodeConfig {

            private String host;
            private String port;
        }
    }

}
