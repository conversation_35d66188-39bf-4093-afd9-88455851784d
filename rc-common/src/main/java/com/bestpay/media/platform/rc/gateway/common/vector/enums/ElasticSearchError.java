package com.bestpay.media.platform.rc.gateway.common.vector.enums;

import co.elastic.clients.elasticsearch._types.ErrorCause;
import com.bestpay.media.platform.rc.gateway.common.error.GatewayError;
import java.util.function.Function;
import lombok.Getter;

/**
 * @Author: 段然
 * @Date: 2024/9/10
 */
@Getter
public enum ElasticSearchError {

    INVALID_PARAM_COLLECTION_NOT_EXIST("index_not_found_exception", GatewayError.INVALID_PARAM_COLLECTION_NOT_EXIST),
    INVALID_PARAM_COLLECTION_ALREADY_EXISTS("resource_already_exists_exception", GatewayError.INVALID_PARAM_COLLECTION_ALREADY_EXISTS),
    INVALID_PARAM_COLLECTION_NAME_INVALID("invalid_index_name_exception", GatewayError.INVALID_PARAM_COLLECTION_NAME_INVALID),
    INVALID_PARAM_COLLECTION_NAME_INVALID2("invalid_alias_name_exception", GatewayError.INVALID_PARAM_COLLECTION_NAME_INVALID),
    BAD_GATEWAY_ELASTICSEARCH_SECURITY_EXCEPTION("security_exception", GatewayError.BAD_GATEWAY_ELASTICSEARCH_SECURITY_EXCEPTION),
    ;

    private final String errorType;

    private final GatewayError gatewayError;

    private final Function<ErrorCause, Object[]> handler;

    ElasticSearchError(String errorType, GatewayError gatewayError) {
        this.errorType = errorType;
        this.gatewayError = gatewayError;
        this.handler = (e) -> null;
    }

    ElasticSearchError(String errorType, GatewayError gatewayError, Function<ErrorCause, Object[]> handler) {
        this.errorType = errorType;
        this.gatewayError = gatewayError;
        this.handler = handler;
    }
}
